# 🚗 电机控制重写完成文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 电机控制系统重写完成
- **目的**: 采用简洁的car.c风格重写电机控制，解决复杂性问题

## 🎯 重写内容

### 移除的文件
- ❌ **APP/motor_app.c**: 复杂的电机应用层
- ❌ **APP/motor_app.h**: 电机应用层头文件
- ❌ **APP/motor_driver.c**: 复杂的电机驱动层
- ❌ **APP/motor_driver.h**: 电机驱动层头文件

### 新增的文件
- ✅ **APP/car.c**: 简洁的电机控制实现
- ✅ **APP/car.h**: 简洁的电机控制头文件

## 🔧 新的car.c架构

### 核心函数设计
```c
// 基础控制
void car_init(void);                    // 初始化
void car_stop(void);                    // 停止
void car_run(int left_speed, int right_speed);  // 双电机控制(核心)

// 便捷控制
void car_forward(int speed);            // 前进
void car_backward(int speed);           // 后退
void car_left(int speed);               // 左转
void car_right(int speed);              // 右转
```

### 简洁的实现特点
1. **直接控制**: 直接操作HAL库函数，无中间层
2. **简单逻辑**: 无复杂的死区补偿和限制
3. **清晰结构**: 每个函数功能单一明确
4. **易于调试**: 代码简洁，便于问题定位

## 📊 函数实现详解

### car_init() - 初始化
```c
void car_init(void)
{
    // 启动PWM
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);  // 左电机
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_2);  // 右电机
    
    // 初始状态停止
    car_stop();
}
```

### car_stop() - 停止
```c
void car_stop(void)
{
    // 停止PWM输出
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, 0);
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, 0);
    
    // 方向控制引脚全部拉低
    HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Bin1_GPIO_Port, Bin1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Bin2_GPIO_Port, Bin2_Pin, GPIO_PIN_RESET);
}
```

### car_run() - 核心控制函数
```c
void car_run(int left_speed, int right_speed)
{
    // 限制速度范围 -1000 到 1000
    if(left_speed > 1000) left_speed = 1000;
    if(left_speed < -1000) left_speed = -1000;
    if(right_speed > 1000) right_speed = 1000;
    if(right_speed < -1000) right_speed = -1000;
    
    // 左电机控制
    if(left_speed >= 0) {
        // 正转
        HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_RESET);
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, left_speed);
    } else {
        // 反转
        HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_SET);
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, -left_speed);
    }
    
    // 右电机控制 (类似逻辑)
}
```

### 便捷函数
```c
void car_forward(int speed)  { car_run(speed, speed); }
void car_backward(int speed) { car_run(-speed, -speed); }
void car_left(int speed)     { car_run(-speed, speed); }
void car_right(int speed)    { car_run(speed, -speed); }
```

## 🔄 系统集成

### scheduler.c中的更新
```c
// 初始化
void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
    car_init();        // 替换Motor_App_Init()
    Mpu6050_Init();
}

// 电机任务
void motor_task()
{
    car_forward(300);  // 替换Motor_App_Forward(100)
}

// 各模式中的替换
case 0: car_stop(); break;                    // 替换Motor_App_Stop()
case 1: car_forward(300); break;              // 替换Motor_App_Forward(25)
case 2: car_run(left_speed, right_speed);     // 替换Motor_App_Set_Speed()
```

### mydefine.h中的更新
```c
// 应用模块头文件
#include "scheduler.h"
#include "key.h"
#include "my_uart.h"
#include "Gray.h"
#include "car.h"           // 替换motor_app.h
#include "mpu6050_app.h"
```

## 🎯 优势对比

### 重写前 (复杂架构)
- ❌ **多层架构**: motor_app + motor_driver 双层
- ❌ **复杂配置**: MOTOR结构体、配置函数
- ❌ **死区补偿**: 复杂的死区补偿逻辑
- ❌ **调试困难**: 多层调用，难以定位问题
- ❌ **编译错误**: 函数声明和实现不匹配

### 重写后 (简洁架构)
- ✅ **单层架构**: 直接car.c控制
- ✅ **简单配置**: 直接HAL库调用
- ✅ **直接控制**: 无复杂的中间处理
- ✅ **易于调试**: 代码简洁，逻辑清晰
- ✅ **编译简单**: 函数少，依赖简单

## 📱 速度参数

### 速度范围
- **范围**: -1000 到 1000
- **PWM周期**: 通常为1000或更高
- **实际速度**: 300-500为中等速度
- **测试速度**: 当前使用300

### 速度对应关系
```c
car_forward(300);   // 中等速度前进
car_forward(500);   // 较快速度前进
car_forward(800);   // 高速前进
car_run(300, 200);  // 左快右慢，右转
car_run(200, 300);  // 左慢右快，左转
```

## 🔍 硬件连接

### PWM输出
- **左电机**: TIM3_CH1 → 左电机PWM输入
- **右电机**: TIM3_CH2 → 右电机PWM输入

### 方向控制
- **左电机**: Ain1_Pin, Ain2_Pin → 左电机方向控制
- **右电机**: Bin1_Pin, Bin2_Pin → 右电机方向控制

### 控制逻辑
```c
// 正转: Ain1=HIGH, Ain2=LOW
// 反转: Ain1=LOW, Ain2=HIGH
// 停止: Ain1=LOW, Ain2=LOW
```

## 🚀 测试验证

### 当前测试
```c
void motor_task()
{
    car_forward(300);  // 强制前进测试
}
```

### 预期效果
- ✅ **电机转动**: 两个电机应该同时前进
- ✅ **PWM输出**: TIM3_CH1和CH2应该输出300的PWM
- ✅ **方向正确**: Ain1/Bin1为HIGH，Ain2/Bin2为LOW
- ✅ **速度适中**: 300速度应该是适中的前进速度

### 调试方法
1. **观察转动**: 直接观察电机是否转动
2. **检查PWM**: 万用表测量PWM输出
3. **检查方向**: 万用表测量方向控制引脚
4. **调整速度**: 修改300为其他值测试

## 📊 系统状态

### 当前配置
- ✅ **文件结构**: car.c + car.h 简洁结构
- ✅ **函数替换**: 所有Motor_App函数已替换为car函数
- ✅ **编译**: 应该无编译错误
- ✅ **测试**: 强制前进300速度

### 功能完整性
- ✅ **初始化**: car_init() 启动PWM
- ✅ **停止**: car_stop() 完全停止
- ✅ **前进**: car_forward() 双电机同速
- ✅ **转向**: car_left/right() 差速转向
- ✅ **精确控制**: car_run() 独立控制双电机

## 🎮 使用建议

### 立即测试
1. **重新编译**: 编译新的car.c架构
2. **下载测试**: 下载到目标板
3. **观察效果**: 电机应该以300速度前进
4. **调整参数**: 根据实际效果调整速度

### 后续优化
1. **速度调优**: 根据实际效果调整速度参数
2. **方向校正**: 如果方向相反，调整GPIO逻辑
3. **循迹集成**: 在循迹模式中使用car_run()
4. **性能测试**: 测试不同速度下的表现

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**重写状态**: ✅ 电机控制系统重写完成  
**架构**: 🚗 简洁的car.c单层架构
