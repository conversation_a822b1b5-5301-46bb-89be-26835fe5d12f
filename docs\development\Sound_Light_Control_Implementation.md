# 🔊 声光控制功能实现文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 已实现
- **目的**: 实现循迹小车的声光控制功能

## 🎯 功能概述

### 触发条件
声光控制在以下情况下触发：
- **边沿检测**: 从白色区域进入黑线时
- **状态变化**: 从无线检测到有线检测的瞬间
- **一次性触发**: 每次状态变化只触发一次

### 控制逻辑
```c
// 检测从无线到有线的状态变化 - 触发声光控制
if(!last_line_detected && current_line_detected) {
    // 从空白区域进入黑线，触发声光控制
    // TODO: 添加GPIO控制 (PF11)
    // HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_RESET); // 触发声光
    
    my_printf(&huart1," [BEEP]");  // 串口提示声光触发
}
```

## 🔧 硬件配置

### GPIO配置
- **引脚**: PF11
- **功能**: 声光控制输出
- **触发方式**: 低电平触发
- **控制逻辑**: 
  - GPIO_PIN_RESET (低电平) = 触发声光
  - GPIO_PIN_SET (高电平) = 关闭声光

### 电路连接
```
STM32F407 PF11 ──► 声光控制模块
                   ├── LED指示灯
                   └── 蜂鸣器
```

## 📊 实现状态

### 当前实现
- ✅ **边沿检测**: 正确检测从无线到有线的状态变化
- ✅ **串口提示**: 输出 "[BEEP]" 提示声光触发
- ✅ **状态管理**: 使用 last_line_detected 记录上次状态
- ⏳ **GPIO控制**: 暂时注释，待GPIO配置完成后启用

### 代码结构
```c
// 在gray_task()中实现
static uint8_t last_line_detected = 0;  // 上次是否检测到线
uint8_t current_line_detected = (active_sensors > 0) ? 1 : 0;  // 当前状态

// 边沿检测
if(!last_line_detected && current_line_detected) {
    // 触发声光控制
    my_printf(&huart1," [BEEP]");
}

// 更新状态
last_line_detected = current_line_detected;
```

## 🎮 使用效果

### 串口输出示例
```
P2:1 P3:1 P4:1 P5:1 P6:1 P7:1 P8:1 | ERR:0.00 ACT:0 | L:20F R:20F | MODE2
P2:1 P3:0 P4:0 P5:1 P6:1 P7:1 P8:1 | ERR:-2.50 ACT:2 | L:28F R:12F | MODE2 [BEEP]
P2:1 P3:0 P4:0 P5:0 P6:1 P7:1 P8:1 | ERR:-2.00 ACT:3 | L:26F R:14F | MODE2
```

说明：
- 第1行: 在白色区域，无线检测
- 第2行: 检测到黑线，触发 [BEEP] 提示
- 第3行: 继续在黑线上，不再触发

### 触发特点
- **一次性**: 每次进入黑线只触发一次
- **边沿敏感**: 只在状态变化时触发
- **实时响应**: 30ms任务周期，快速响应

## 🔧 GPIO配置步骤

### 1. CubeMX配置
```
1. 打开CubeMX项目
2. 找到PF11引脚
3. 配置为GPIO_Output
4. 设置为Push Pull模式
5. 设置初始状态为High (关闭状态)
6. 重新生成代码
```

### 2. 代码启用
```c
// 在scheduler.c中启用GPIO控制
if(!last_line_detected && current_line_detected) {
    // 启用GPIO控制
    HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_RESET); // 触发声光
    HAL_Delay(100);  // 持续100ms
    HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_SET);   // 关闭声光
    
    my_printf(&huart1," [BEEP]");
}
```

### 3. 非阻塞实现 (推荐)
```c
// 非阻塞声光控制
static uint32_t beep_start_time = 0;
static uint8_t beep_active = 0;

if(!last_line_detected && current_line_detected && !beep_active) {
    // 开始声光控制
    beep_start_time = HAL_GetTick();
    beep_active = 1;
    HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_RESET);
}

if(beep_active && (HAL_GetTick() - beep_start_time >= 100)) {
    // 结束声光控制
    beep_active = 0;
    HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_SET);
}
```

## 🎯 应用场景

### 循迹调试
- **线路检测**: 确认传感器正确检测到黑线
- **状态监控**: 实时了解小车的循迹状态
- **问题诊断**: 通过声光提示发现传感器问题

### 比赛应用
- **起始提示**: 小车开始循迹时的声光提示
- **关键点提示**: 在关键路径点的状态提示
- **状态反馈**: 向操作者提供实时状态反馈

### 演示效果
- **视觉效果**: LED闪烁增强演示效果
- **听觉效果**: 蜂鸣器提示增强用户体验
- **交互反馈**: 提供直观的系统状态反馈

## ⚠️ 注意事项

### 1. GPIO配置
- 确保PF11引脚在CubeMX中正确配置
- 检查引脚是否与其他功能冲突
- 验证GPIO初始化代码

### 2. 时序控制
- 避免使用HAL_Delay()阻塞任务调度
- 推荐使用非阻塞的时间控制
- 控制声光持续时间合理

### 3. 硬件连接
- 确认声光模块的触发电平
- 检查电流驱动能力
- 验证电路连接正确性

### 4. 调试建议
- 先使用串口输出验证逻辑
- 再启用GPIO控制
- 逐步验证功能正确性

## 🔄 未来优化

### 1. 多种提示模式
- 不同频率的蜂鸣器提示
- 不同颜色的LED指示
- 不同模式的声光组合

### 2. 可配置控制
- 声光开关控制
- 提示强度调节
- 自定义提示模式

### 3. 智能控制
- 根据环境自动调节
- 基于状态的智能提示
- 节能模式控制

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**技术支持**: STM32循迹小车开发团队
