# 📋 MPU6050增强循迹系统产品需求文档

## 📋 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-07
- **负责人**: Emma (产品经理)
- **状态**: 设计完成
- **目的**: 集成MPU6050姿态数据到循迹算法，实现智能姿态辅助循迹

## 🎯 背景与问题陈述

### 当前循迹系统局限
1. **单一传感器依赖**: 仅依赖灰度传感器，缺乏姿态信息
2. **转向不精确**: 无法感知实际转向角度，容易偏离路径
3. **坡道适应差**: 上下坡时无法调整速度和姿态
4. **稳定性不足**: 无法检测车体倾斜和不稳定状态

### MPU6050优势
- ✅ **实时姿态**: 提供俯仰角、横滚角、偏航角
- ✅ **高精度**: ±0.2°精度，校准系统完善
- ✅ **快速响应**: 50Hz更新频率
- ✅ **稳定可靠**: 数字滤波，抗干扰能力强

## 🎯 目标与成功指标

### 项目目标 (Objectives)
1. **提升循迹精度**: 结合姿态数据优化路径跟踪
2. **增强环境适应**: 自动适应坡道和复杂路况
3. **提高稳定性**: 实时监控车体稳定性
4. **智能速度控制**: 基于姿态动态调整速度

### 关键结果 (Key Results)
- **循迹精度提升**: 路径偏差减少50%
- **转向精度**: 转向角度误差<5°
- **坡道适应**: 自动识别并适应±15°坡道
- **稳定性**: 检测并预防倾翻风险

### 反向指标 (Counter Metrics)
- **响应延迟**: 不超过20ms
- **计算负载**: CPU使用率增加<10%
- **功耗增加**: 系统功耗增加<5%

## 👥 用户画像与用户故事

### 目标用户
- **竞赛参与者**: 需要高精度循迹性能
- **技术爱好者**: 追求先进的控制算法
- **教育用户**: 学习姿态控制和传感器融合

### 用户故事
1. **作为竞赛选手**，我希望小车能精确跟踪复杂路径，以获得更好成绩
2. **作为技术爱好者**，我希望看到姿态数据如何改善循迹效果
3. **作为学习者**，我希望理解多传感器融合的实际应用

## 🔧 功能规格详述

### 1. 姿态辅助转向控制

#### 功能描述
结合偏航角数据修正转向控制，确保精确的方向控制。

#### 业务逻辑
```c
// 姿态辅助转向算法
float target_yaw = calculate_target_yaw(line_position);
float yaw_error = target_yaw - current_yaw;
int8_t yaw_correction = (int8_t)(yaw_error * YAW_KP);

// 应用到电机控制
left_speed = base_speed + line_correction + yaw_correction;
right_speed = base_speed - line_correction - yaw_correction;
```

#### 边缘情况
- 偏航角跳变处理（±180°边界）
- 传感器故障时的降级处理
- 快速转向时的稳定性保证

### 2. 坡道自适应控制

#### 功能描述
基于俯仰角自动识别坡道，调整电机功率和控制参数。

#### 业务逻辑
```c
// 坡道检测与适应
if(fabs(Pitch) > SLOPE_THRESHOLD) {
    if(Pitch > 0) {
        // 上坡：增加功率，降低速度
        speed_factor = UPHILL_SPEED_FACTOR;
        power_boost = UPHILL_POWER_BOOST;
    } else {
        // 下坡：减少功率，增强制动
        speed_factor = DOWNHILL_SPEED_FACTOR;
        brake_factor = DOWNHILL_BRAKE_FACTOR;
    }
}
```

#### 参数配置
- **坡道阈值**: 5°触发坡道模式
- **上坡增益**: 功率+20%，速度-30%
- **下坡控制**: 功率-10%，制动+50%

### 3. 稳定性监控系统

#### 功能描述
实时监控横滚角，检测车体倾斜和潜在翻车风险。

#### 业务逻辑
```c
// 稳定性监控
if(fabs(Roll) > STABILITY_WARNING) {
    if(fabs(Roll) > STABILITY_CRITICAL) {
        // 紧急停车
        emergency_stop();
    } else {
        // 稳定性修正
        stability_correction = Roll * STABILITY_KP;
        apply_stability_correction(stability_correction);
    }
}
```

#### 安全阈值
- **警告阈值**: 15°横滚角
- **危险阈值**: 25°横滚角
- **紧急停车**: 30°横滚角

### 4. 智能速度控制

#### 功能描述
基于姿态数据动态调整基础速度，优化循迹性能。

#### 业务逻辑
```c
// 智能速度控制
float attitude_factor = calculate_attitude_factor(Pitch, Roll, Yaw);
float dynamic_speed = base_speed * attitude_factor;

// 速度限制
if(dynamic_speed > MAX_SPEED) dynamic_speed = MAX_SPEED;
if(dynamic_speed < MIN_SPEED) dynamic_speed = MIN_SPEED;
```

#### 速度策略
- **平地**: 100%基础速度
- **轻微坡道**: 80%基础速度
- **陡峭坡道**: 60%基础速度
- **不稳定状态**: 40%基础速度

## 📊 范围定义

### 包含功能 (In Scope)
- ✅ 姿态辅助转向控制
- ✅ 坡道自适应控制
- ✅ 稳定性监控系统
- ✅ 智能速度控制
- ✅ 多传感器数据融合
- ✅ 实时姿态数据显示

### 排除功能 (Out of Scope)
- ❌ 复杂的卡尔曼滤波算法
- ❌ 机器学习路径预测
- ❌ 多车协同控制
- ❌ 无线遥控功能

## 🔗 依赖与风险

### 内部依赖
- **MPU6050校准系统**: 必须正常工作
- **灰度传感器**: 提供基础循迹数据
- **电机控制系统**: 支持精确速度控制
- **任务调度器**: 保证实时性

### 外部依赖
- **硬件稳定性**: MPU6050硬件连接可靠
- **环境条件**: 测试场地具备不同坡度
- **电源供应**: 稳定的电源供应

### 潜在风险
- **传感器冲突**: 姿态数据与灰度数据冲突
- **响应延迟**: 多传感器处理增加延迟
- **参数调优**: 需要大量测试调优参数
- **稳定性**: 复杂算法可能影响系统稳定性

## 🚀 发布初步计划

### 开发阶段
1. **算法设计**: 2小时 - 设计融合算法
2. **代码实现**: 3小时 - 实现核心功能
3. **参数调优**: 2小时 - 优化控制参数
4. **测试验证**: 3小时 - 全面测试验证

### 测试计划
- **单元测试**: 各功能模块独立测试
- **集成测试**: 多传感器融合测试
- **场景测试**: 不同路况和坡度测试
- **性能测试**: 响应时间和精度测试

### 发布标准
- ✅ 所有功能正常工作
- ✅ 循迹精度明显提升
- ✅ 坡道适应能力验证
- ✅ 稳定性监控有效

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**设计状态**: ✅ 需求分析完成  
**下一步**: 技术架构设计
