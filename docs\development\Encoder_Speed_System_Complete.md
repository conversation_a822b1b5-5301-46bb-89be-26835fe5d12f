# 🏃 编码器测速系统完成文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 编码器测速功能完成
- **目的**: 为循迹小车添加实时速度监控功能

## 🎯 测速系统架构

### 新增文件
- ✅ **APP/encoder.h**: 编码器测速头文件
- ✅ **APP/encoder.c**: 编码器测速实现文件

### 硬件配置
- ✅ **左编码器**: TIM1 (编码器模式)
- ✅ **右编码器**: TIM2 (编码器模式)
- ✅ **采样频率**: 20ms (50Hz)

## 🔧 核心功能

### 编码器参数配置
```c
#define ENCODER_PPR 1040        // 编码器每转脉冲数 (13线*20减速比*4倍频)
#define WHEEL_DIAMETER_CM 6.5f  // 车轮直径(cm)
#define WHEEL_CIRCUMFERENCE_CM (WHEEL_DIAMETER_CM * PI)  // 车轮周长
#define SAMPLING_TIME_S 0.02f   // 采样时间20ms
```

### 编码器数据结构
```c
typedef struct {
    TIM_HandleTypeDef *htim;    // 定时器句柄
    uint8_t reverse;            // 是否反向 (0-正常, 1-反向)
    int16_t count;              // 当前周期计数
    int32_t total_count;        // 累计总计数
    float speed_cm_s;           // 速度 cm/s
    float speed_rpm;            // 转速 RPM
} Encoder_t;
```

### 核心函数
```c
void encoder_init(void);           // 初始化编码器
void encoder_update(void);         // 更新编码器数据 (20ms调用)
float encoder_get_left_speed(void);  // 获取左轮速度 (cm/s)
float encoder_get_right_speed(void); // 获取右轮速度 (cm/s)
float encoder_get_left_rpm(void);    // 获取左轮转速 (RPM)
float encoder_get_right_rpm(void);   // 获取右轮转速 (RPM)
```

## 📊 速度计算原理

### 线速度计算 (cm/s)
```c
speed_cm_s = count / ENCODER_PPR * WHEEL_CIRCUMFERENCE_CM / SAMPLING_TIME_S

其中:
- count: 20ms内的编码器计数
- ENCODER_PPR: 1040 (每转脉冲数)
- WHEEL_CIRCUMFERENCE_CM: 6.5 * π ≈ 20.4 cm
- SAMPLING_TIME_S: 0.02s (20ms)
```

### 转速计算 (RPM)
```c
speed_rpm = count / ENCODER_PPR * 60 / SAMPLING_TIME_S

其中:
- 60: 每分钟60秒
- 其他参数同上
```

### 计算示例
```
假设20ms内计数为52:
线速度 = 52 / 1040 * 20.4 / 0.02 = 51 cm/s
转速 = 52 / 1040 * 60 / 0.02 = 150 RPM
```

## 🔄 系统集成

### 任务调度集成
```c
static task_t scheduler_task[] =
{
    {key_task,100,0},     // 按键任务
    {uart_task,100,0},    // 串口任务
    {motor_task,20,0},    // 电机任务
    {encoder_task,20,0},  // 编码器任务 ✅ (新增)
    {mpu_task,50,0},      // MPU6050任务
    {gray_task,20,0},     // 灰度传感器任务
};
```

### 初始化集成
```c
void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
    car_init();        // 电机初始化
    encoder_init();    // 编码器初始化 ✅ (新增)
    Mpu6050_Init();    // MPU6050初始化
}
```

### 编码器任务
```c
void encoder_task()
{
    encoder_update();  // 20ms周期更新编码器数据
}
```

## 📱 串口输出增强

### 新的输出格式
```
[M0] 1111111 | Err:0.00 | Act:0 | P:0.1 R:0.1 Y:0.1 | YErr:0.0 | Dir:0 | Pre:0 Ture:0 | L:51.2 R:48.7

新增字段:
- L:51.2  → 左轮速度 (cm/s)
- R:48.7  → 右轮速度 (cm/s)
```

### 速度监控优势
- ✅ **实时监控**: 实时显示左右轮速度
- ✅ **差速检测**: 可以检测左右轮速度差异
- ✅ **性能分析**: 分析电机控制效果
- ✅ **故障诊断**: 检测电机或编码器故障

## 🎯 编码器配置

### 硬件连接
```
左编码器 (TIM1):
- A相: TIM1_CH1 (通常PA8)
- B相: TIM1_CH2 (通常PA9)

右编码器 (TIM2):
- A相: TIM2_CH1 (通常PA0)
- B相: TIM2_CH2 (通常PA1)
```

### 编码器模式
```c
// 在CubeMX中配置:
TIM1: Encoder Mode
TIM2: Encoder Mode
Counter Period: 65535 (16位)
```

### 方向配置
```c
left_encoder.reverse = 0;   // 左编码器正常方向
right_encoder.reverse = 1;  // 右编码器反向 (根据安装调整)
```

## 🔍 测速精度

### 分辨率计算
```
角度分辨率 = 360° / 1040 ≈ 0.35°
距离分辨率 = 20.4cm / 1040 ≈ 0.02cm
速度分辨率 = 0.02cm / 0.02s = 1 cm/s
```

### 测量范围
```
最小可测速度: 1 cm/s (1个脉冲/20ms)
最大可测速度: 约1000 cm/s (理论值)
实际使用范围: 5-200 cm/s
```

### 精度影响因素
- ✅ **采样频率**: 20ms采样，精度适中
- ✅ **编码器分辨率**: 1040 PPR，精度较高
- ✅ **车轮直径**: 6.5cm，需要精确测量
- ✅ **减速比**: 20:1，已考虑在PPR中

## 📊 应用场景

### 电机控制优化
```c
// 可以基于实际速度进行PID控制
float target_speed = 50.0f;  // 目标速度 cm/s
float actual_speed = encoder_get_left_speed();
float error = target_speed - actual_speed;
// PID控制逻辑...
```

### 循迹性能分析
```c
// 分析循迹时的速度变化
float left_speed = encoder_get_left_speed();
float right_speed = encoder_get_right_speed();
float speed_diff = left_speed - right_speed;
// 分析转向性能...
```

### 里程计算
```c
// 可以计算行驶距离
static float total_distance = 0;
float avg_speed = (encoder_get_left_speed() + encoder_get_right_speed()) / 2;
total_distance += avg_speed * SAMPLING_TIME_S;
```

## 🚀 测试验证

### 静态测试
1. **编码器计数**: 手动转动车轮，观察计数变化
2. **方向测试**: 确认正转/反转方向正确
3. **速度计算**: 验证速度计算公式正确

### 动态测试
1. **电机测试**: 运行电机，观察速度输出
2. **一致性测试**: 相同PWM下左右轮速度应接近
3. **响应测试**: 速度变化的响应时间

### 预期结果
```
静止状态: L:0.0 R:0.0
前进状态: L:50.0 R:48.0 (略有差异正常)
左转状态: L:20.0 R:60.0 (左慢右快)
右转状态: L:60.0 R:20.0 (左快右慢)
```

## 🔧 调试建议

### 如果速度为0
1. **检查编码器连接**: 确认A/B相连接正确
2. **检查定时器配置**: 确认编码器模式配置
3. **检查方向设置**: 调整reverse参数
4. **检查车轮参数**: 确认直径和PPR设置

### 如果速度异常
1. **校准车轮直径**: 精确测量车轮直径
2. **校准PPR**: 确认编码器线数和减速比
3. **检查采样时间**: 确认20ms采样周期
4. **检查计算公式**: 验证速度计算逻辑

## 📱 使用建议

### 立即测试
1. **重新编译**: 编译新增的编码器功能
2. **下载测试**: 下载到目标板
3. **观察串口**: 查看速度输出是否正常
4. **手动测试**: 手动转动车轮测试编码器

### 后续优化
1. **PID控制**: 基于速度反馈实现闭环控制
2. **速度限制**: 根据实际速度限制PWM输出
3. **里程统计**: 实现行驶距离统计功能
4. **性能分析**: 分析不同模式下的速度特性

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**功能状态**: ✅ 编码器测速系统完成  
**集成状态**: 🔄 已集成到car.c系统和任务调度
