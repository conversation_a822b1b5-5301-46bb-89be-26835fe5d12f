#include "mydefine.h"

// 循迹小车系统

// TEXT协议宏定义 - 纸飞机调试工具
#define PRINT(window, fmt, ...) my_printf(&huart1, "{" #window "}" fmt "\n", ##__VA_ARGS__)

// 纯循迹小车系统 - 专注循迹核心功能

uint8_t gray_buff[8];

void uart_task()
{
	// 循迹状态输出
	static uint8_t output_count = 0;

	// 每200ms输出一次状态 (100ms * 2 = 200ms)
	if(++output_count >= 2) {
		output_count = 0;

		// 获取循迹数据
		float position_error = Gray_Get_Position_Error();
		uint8_t active_sensors = Gray_Get_Active_Sensor_Count();

		// 获取MPU6050数据
		float yaw_error = Get_Yaw_Error();
		int8_t direction_correction = Get_Direction_Correction();

		// 输出格式: [模式] 传感器状态 | 循迹误差 | 激活数 | 姿态角 | 方向误差 | 方向修正 | 按键状态
		my_printf_nb(&huart1, "[M%d] %d%d%d%d%d%d%d | Err:%.2f | Act:%d | P:%.1f R:%.1f Y:%.1f | YErr:%.1f | Dir:%d | Pre:%d Ture:%d\r\n",
		          sys_mode,
		          gray_buff[1], gray_buff[2], gray_buff[3], gray_buff[4],
		          gray_buff[5], gray_buff[6], gray_buff[7],
		          position_error, active_sensors, Pitch, Roll, Yaw, yaw_error, direction_correction, pre_mode, sys_ture);
	}

	// 处理串口接收数据 - 规整化输出
	if(uart_rx_index == 0)return;

	if(HAL_GetTick() - uart_rx_ticks > 100)
	{
		// 格式: [RX] Command: reset
		my_printf_nb(&huart1,"[RX] Command: %s\r\n", uart_rx_buffer);

		memset(uart_rx_buffer,0,uart_rx_index);
		uart_rx_index = 0;
		huart1.pRxBuffPtr = uart_rx_buffer;
	}
}

void gray_task()
{
	// 执行基于2024_H_Car蓝本的灰度传感器任务
	Gray_Task();

	// 获取传感器数据 (兼容原接口)
	Gray_get(gray_buff);

	// 获取巡线位置误差和激活传感器数量
	float position_error = Gray_Get_Position_Error();
	uint8_t active_sensors = Gray_Get_Active_Sensor_Count();

	// 声光控制：从白色区域进入黑线时触发
	static uint8_t last_line_detected = 0;  // 上次是否检测到线
	uint8_t current_line_detected = (active_sensors > 0) ? 1 : 0;  // 当前是否检测到线

	static unsigned long beep_start_time = 0;
	static uint8_t beep_active = 0;

	// 检测从无线到有线的状态变化 - 触发声光控制
	if(!last_line_detected && current_line_detected && !beep_active) {
		// 开始声光控制 (已禁用)
		beep_start_time = HAL_GetTick();
		beep_active = 1;
		// HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_RESET); // 触发beep+LED (PF11) - 暂时禁用
	}

	// 检查是否需要关闭声光 (100ms后自动关闭)
	if(beep_active && (HAL_GetTick() - beep_start_time >= 100)) {
		beep_active = 0;
		// HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_SET);   // 关闭beep+LED (PF11) - 暂时禁用
	}

	// 更新上次状态
	last_line_detected = current_line_detected;

	// 根据模式执行不同的控制逻辑 (使用新电机库)
	switch(sys_mode) {
		case 0:  // 模式0：停止模式
			car_stop();
			break;

		case 1:  // 模式1：直线行驶，遇黑线后停止直到切换模式
			{
				static uint8_t line_stopped = 0;     // 遇线停止标志
				static uint8_t last_mode = 0;        // 记录上次模式

				// 检测模式切换，重置停止标志
				if(last_mode != sys_mode) {
					line_stopped = 0;  // 重置停止状态
					last_mode = sys_mode;
				}

				if(line_stopped) {
					// 已经因为黑线停止，保持停止状态，直到切换模式
					car_stop();
				} else if(active_sensors > 0) {
					// 检测到黑线，立即停止
					car_stop();
					line_stopped = 1;  // 设置停止标志
				} else {
					// 没有检测到黑线，继续直行
					car_forward(300);
				}
			}
			break;

		case 2:  // 模式2：简单循迹模式 (MPU6050增强)
			{
				static uint8_t lost_line_count = 0;  // 丢线计数器
				static uint8_t mode_init = 0;        // 模式初始化标志

				// 模式初始化：设置目标方向
				if(!mode_init) {
					Reset_Target_Direction();  // 设置当前方向为目标方向
					mode_init = 1;
				}

				// 获取方向修正
				int8_t direction_correction = Get_Direction_Correction();

				// 基础速度 (降低起始速度)
				uint8_t base_speed = 20;

				if(active_sensors > 0) {
					// 检测到线路，重置丢线计数
					lost_line_count = 0;

					// 根据误差大小动态调整速度
					float abs_error = (position_error < 0) ? -position_error : position_error;

					if(abs_error > 2.0f) {
						// 大偏差：降低速度，增强转向
						base_speed = 15;
					} else if(abs_error > 1.0f) {
						// 中等偏差：正常速度
						base_speed = 20;
					} else {
						// 小偏差：适当提高速度
						base_speed = 25;
					}

					// 计算循迹修正值 (降低修正强度)
					int8_t line_correction = (int8_t)(position_error * 3.0f);

					// 融合循迹修正和方向修正
					int8_t total_correction = line_correction + direction_correction;

					// 限制总修正范围
					if(total_correction > 25) total_correction = 25;
					if(total_correction < -25) total_correction = -25;

					// 计算左右轮速度
					int8_t left_speed = base_speed - total_correction;
					int8_t right_speed = base_speed + total_correction;

					// 速度限制保护
					if(left_speed < 5) left_speed = 5;
					if(left_speed > 50) left_speed = 50;
					if(right_speed < 5) right_speed = 5;
					if(right_speed > 50) right_speed = 50;

					// 执行融合控制
					car_run(left_speed, right_speed);
				}
				else {
					// 丢线处理策略 - 使用MPU6050保持方向
					lost_line_count++;

					if(lost_line_count < 10) {
						// 短暂丢线：使用MPU6050保持方向前进
						int8_t left_speed = 20 - direction_correction;
						int8_t right_speed = 20 + direction_correction;

						// 速度限制
						if(left_speed < 5) left_speed = 5;
						if(left_speed > 30) left_speed = 30;
						if(right_speed < 5) right_speed = 5;
						if(right_speed > 30) right_speed = 30;

						car_run(left_speed, right_speed);
					} else if(lost_line_count < 30) {
						// 持续丢线：降速搜索，保持方向
						int8_t left_speed = 15 - direction_correction;
						int8_t right_speed = 15 + direction_correction;

						// 速度限制
						if(left_speed < 5) left_speed = 5;
						if(left_speed > 25) left_speed = 25;
						if(right_speed < 5) right_speed = 5;
						if(right_speed > 25) right_speed = 25;

						car_run(left_speed, right_speed);
					} else {
						// 长时间丢线：停止等待
						car_stop();
						lost_line_count = 30; // 防止溢出
					}
				}
			}
			break;

		case 3:  // 模式3：(预留)
			car_stop();
			break;

		default:
			// 默认模式：停止
			car_stop();
			break;
	}
}

uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    unsigned long rate_ms;
    unsigned long last_run;
} task_t;


static task_t scheduler_task[] =
{
		{key_task,100,0},     // 按键任务，100ms周期
		{uart_task,100,0},    // 串口任务，100ms周期 (降低频率提升性能)
		{motor_task,20,0},    // 电机任务，20ms周期 (保持高频控制)
		{mpu_task,50,0},      // MPU6050任务，50ms周期 (适中频率)
		{gray_task,20,0},     // 灰度传感器任务，20ms周期 (提高循迹精度)
};

void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
    // 初始化电机系统
    car_init();
    // 初始化MPU6050系统
    Mpu6050_Init();
}

void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        unsigned long now_time = HAL_GetTick();

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;

            scheduler_task[i].task_func();
        }
    }
}

/* 电机控制任务 */
void motor_task()
{
	// 强制电机前进测试 - 忽略所有模式
	car_forward(300);
}

/* MPU6050任务 */
void mpu_task()
{
	// 执行MPU6050数据读取和方向控制
	Mpu6050_Task();
}
