/*
STM32 OLED(ssd1306) driver - Complete implementation for tracking car
*/

#include "oled.h"
#include "oledfont.h"
#include "main.h"
#include <string.h>
#include <stdio.h>

// OLED显示缓冲区
static uint8_t OLED_GRAM[128][8];

// 软件I2C实现
#define OLED_SCL_PIN    GPIO_PIN_14  // PE14
#define OLED_SDA_PIN    GPIO_PIN_15  // PE15
#define OLED_SCL_PORT   GPIOE
#define OLED_SDA_PORT   GPIOE

// I2C操作宏
#define OLED_SCL_HIGH() HAL_GPIO_WritePin(OLED_SCL_PORT, OLED_SCL_PIN, GPIO_PIN_SET)
#define OLED_SCL_LOW()  HAL_GPIO_WritePin(OLED_SCL_PORT, OLED_SCL_PIN, GPIO_PIN_RESET)
#define OLED_SDA_HIGH() HAL_GPIO_WritePin(OLED_SDA_PORT, OLED_SDA_PIN, GPIO_PIN_SET)
#define OLED_SDA_LOW()  HAL_GPIO_WritePin(OLED_SDA_PORT, OLED_SDA_PIN, GPIO_PIN_RESET)
#define OLED_SDA_READ() HAL_GPIO_ReadPin(OLED_SDA_PORT, OLED_SDA_PIN)

// 延时函数
static void OLED_Delay_us(uint32_t us)
{
    uint32_t delay = us * 168 / 4;  // 168MHz系统时钟
    while(delay--);
}

// I2C起始信号
static void OLED_I2C_Start(void)
{
    OLED_SDA_HIGH();
    OLED_SCL_HIGH();
    OLED_Delay_us(5);
    OLED_SDA_LOW();
    OLED_Delay_us(5);
    OLED_SCL_LOW();
}

// I2C停止信号
static void OLED_I2C_Stop(void)
{
    OLED_SDA_LOW();
    OLED_SCL_HIGH();
    OLED_Delay_us(5);
    OLED_SDA_HIGH();
    OLED_Delay_us(5);
}

// I2C发送一个字节
static void OLED_I2C_SendByte(uint8_t byte)
{
    for(int i = 7; i >= 0; i--)
    {
        if(byte & (1 << i))
            OLED_SDA_HIGH();
        else
            OLED_SDA_LOW();

        OLED_Delay_us(2);
        OLED_SCL_HIGH();
        OLED_Delay_us(5);
        OLED_SCL_LOW();
        OLED_Delay_us(2);
    }

    // 等待ACK
    OLED_SDA_HIGH();
    OLED_Delay_us(2);
    OLED_SCL_HIGH();
    OLED_Delay_us(5);
    OLED_SCL_LOW();
    OLED_Delay_us(2);
}

// OLED写命令
void OLED_Write_cmd(uint8_t cmd)
{
    OLED_I2C_Start();
    OLED_I2C_SendByte(OLED_ADDR);  // 发送设备地址
    OLED_I2C_SendByte(0x00);       // 命令模式
    OLED_I2C_SendByte(cmd);        // 发送命令
    OLED_I2C_Stop();
}

// OLED写数据
void OLED_Write_data(uint8_t data)
{
    OLED_I2C_Start();
    OLED_I2C_SendByte(OLED_ADDR);  // 发送设备地址
    OLED_I2C_SendByte(0x40);       // 数据模式
    OLED_I2C_SendByte(data);       // 发送数据
    OLED_I2C_Stop();
}

// 设置显示位置
void OLED_Set_Position(uint8_t x, uint8_t y)
{
    OLED_Write_cmd(0xb0 + y);                    // 设置页地址
    OLED_Write_cmd(((x & 0xf0) >> 4) | 0x10);   // 设置列地址高4位
    OLED_Write_cmd((x & 0x0f) | 0x01);          // 设置列地址低4位
}

// 更新显示
static void OLED_UpdateScreen(void)
{
    for(uint8_t i = 0; i < 8; i++)
    {
        OLED_Set_Position(0, i);
        for(uint8_t j = 0; j < 128; j++)
        {
            OLED_Write_data(OLED_GRAM[j][i]);
        }
    }
}

// 在指定位置画点
static void OLED_DrawPoint(uint8_t x, uint8_t y, uint8_t color)
{
    if(x >= 128 || y >= 64) return;

    if(color)
        OLED_GRAM[x][y / 8] |= (1 << (y % 8));
    else
        OLED_GRAM[x][y / 8] &= ~(1 << (y % 8));
}

// 显示字符
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t ch, uint8_t fontsize)
{
    ch = ch - ' ';  // 得到偏移后的值

    if(fontsize == 16)
    {
        // 8x16字体
        for(uint8_t i = 0; i < 8; i++)
        {
            for(uint8_t j = 0; j < 8; j++)
            {
                if(F8X16[ch * 16 + i] & (0x80 >> j))
                    OLED_DrawPoint(x + j, y + i, 1);
                else
                    OLED_DrawPoint(x + j, y + i, 0);
            }
        }
        for(uint8_t i = 0; i < 8; i++)
        {
            for(uint8_t j = 0; j < 8; j++)
            {
                if(F8X16[ch * 16 + i + 8] & (0x80 >> j))
                    OLED_DrawPoint(x + j, y + i + 8, 1);
                else
                    OLED_DrawPoint(x + j, y + i + 8, 0);
            }
        }
    }
    else // 6x8字体
    {
        for(uint8_t i = 0; i < 6; i++)
        {
            for(uint8_t j = 0; j < 8; j++)
            {
                if(F6X8[ch][i] & (1 << j))
                    OLED_DrawPoint(x + i, y + j, 1);
                else
                    OLED_DrawPoint(x + i, y + j, 0);
            }
        }
    }
}

// 显示字符串
void OLED_ShowStr(uint8_t x, uint8_t y, char *ch, uint8_t fontsize)
{
    uint8_t x0 = x;
    while(*ch)
    {
        if(x0 > 128 - fontsize/2) break;
        OLED_ShowChar(x0, y, *ch, fontsize);
        x0 += (fontsize == 16) ? 8 : 6;
        ch++;
    }
    OLED_UpdateScreen();
}

// 显示数字
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t length, uint8_t fontsize)
{
    char str[12];
    sprintf(str, "%*lu", length, num);
    OLED_ShowStr(x, y, str, fontsize);
}

// 显示浮点数
void OLED_ShowFloat(uint8_t x, uint8_t y, float num, uint8_t accuracy, uint8_t fontsize)
{
    char str[16];
    sprintf(str, "%.*f", accuracy, num);
    OLED_ShowStr(x, y, str, fontsize);
}

// 清屏
void OLED_Clear(void)
{
    memset(OLED_GRAM, 0, sizeof(OLED_GRAM));
    OLED_UpdateScreen();
}

// 全屏填充
void OLED_Allfill(void)
{
    memset(OLED_GRAM, 0xFF, sizeof(OLED_GRAM));
    OLED_UpdateScreen();
}

// 显示开
void OLED_Display_On(void)
{
    OLED_Write_cmd(0x8D);  // 电荷泵设置
    OLED_Write_cmd(0x14);  // 开启电荷泵
    OLED_Write_cmd(0xAF);  // 开启显示
}

// 显示关
void OLED_Display_Off(void)
{
    OLED_Write_cmd(0x8D);  // 电荷泵设置
    OLED_Write_cmd(0x10);  // 关闭电荷泵
    OLED_Write_cmd(0xAE);  // 关闭显示
}

// OLED初始化
void OLED_Init(void)
{
    // 配置GPIO
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 使能GPIOE时钟
    __HAL_RCC_GPIOE_CLK_ENABLE();

    // 配置SCL和SDA引脚
    GPIO_InitStruct.Pin = OLED_SCL_PIN | OLED_SDA_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;  // 开漏输出
    GPIO_InitStruct.Pull = GPIO_PULLUP;          // 上拉
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

    // 初始化引脚状态
    OLED_SCL_HIGH();
    OLED_SDA_HIGH();

    HAL_Delay(100);  // 等待OLED上电稳定

    // OLED初始化序列
    OLED_Write_cmd(0xAE); // 关闭显示
    OLED_Write_cmd(0x20); // 设置内存地址模式
    OLED_Write_cmd(0x10); // 00,水平地址模式;01,垂直地址模式;10,页地址模式(RESET);11,无效
    OLED_Write_cmd(0xb0); // 设置页起始地址为页0
    OLED_Write_cmd(0xc8); // 设置COM扫描方向
    OLED_Write_cmd(0x00); // 设置低列地址
    OLED_Write_cmd(0x10); // 设置高列地址
    OLED_Write_cmd(0x40); // 设置起始行地址
    OLED_Write_cmd(0x81); // 设置对比度控制寄存器
    OLED_Write_cmd(0xFF); // 设置对比度
    OLED_Write_cmd(0xa1); // 设置段重定义设置,bit0:0,0->0;1,0->127;
    OLED_Write_cmd(0xa6); // 设置显示方式;bit0:1,反相显示;0,正常显示
    OLED_Write_cmd(0xa8); // 设置驱动路数
    OLED_Write_cmd(0x3F); // 默认0X3F(1/64)
    OLED_Write_cmd(0xa4); // 全局显示开启;bit0:1,开启;0,关闭;(白屏/黑屏)
    OLED_Write_cmd(0xd3); // 设置显示偏移
    OLED_Write_cmd(0x00); // 默认为0
    OLED_Write_cmd(0xd5); // 设置时钟分频因子,震荡频率
    OLED_Write_cmd(0x80); // [3:0],分频因子;[7:4],震荡频率
    OLED_Write_cmd(0xd9); // 设置预充电周期
    OLED_Write_cmd(0xf1); // [3:0],PHASE 1;[7:4],PHASE 2;
    OLED_Write_cmd(0xda); // 设置COM硬件引脚配置
    OLED_Write_cmd(0x12); // [5:4]配置
    OLED_Write_cmd(0xdb); // 设置VCOMH 电压倍率
    OLED_Write_cmd(0x40); // [6:4] 000,0.65*vcc;001,0.77*vcc;011,0.83*vcc;
    OLED_Write_cmd(0x8d); // 电荷泵设置
    OLED_Write_cmd(0x14); // bit2，开启/关闭
    OLED_Write_cmd(0xaf); // 开启显示

    OLED_Clear();  // 清屏
}

// 图片显示和汉字显示(简化实现)
void OLED_ShowPic(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint8_t BMP[])
{
    // 简化实现 - 暂时为空
}

void OLED_ShowHanzi(uint8_t x, uint8_t y, uint8_t no)
{
    // 简化实现 - 暂时为空
}

void OLED_ShowHzbig(uint8_t x, uint8_t y, uint8_t n)
{
    // 简化实现 - 暂时为空
}
