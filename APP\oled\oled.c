/*
STM32 OLED(ssd1306) driver - Simplified version for tracking car
*/

#include "oled.h"
#include "oledfont.h"
#include "main.h"
#include <string.h>
#include <stdio.h>

// 简化的OLED实现 - 基础功能

void OLED_Write_cmd(uint8_t cmd)
{
    // 简化实现 - 暂时为空
    // 在实际硬件上需要实现I2C通信
}

void OLED_Write_data(uint8_t data)
{
    // 简化实现 - 暂时为空
    // 在实际硬件上需要实现I2C通信
}

void OLED_ShowPic(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint8_t BMP[])
{
    // 简化实现 - 暂时为空
}

void OLED_ShowHanzi(uint8_t x, uint8_t y, uint8_t no)
{
    // 简化实现 - 暂时为空
}

void OLED_ShowHzbig(uint8_t x, uint8_t y, uint8_t n)
{
    // 简化实现 - 暂时为空
}

void OLED_ShowFloat(uint8_t x, uint8_t y, float num, uint8_t accuracy, uint8_t fontsize)
{
    // 简化实现 - 暂时为空
    // 在实际硬件上需要实现浮点数显示
}

void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t length, uint8_t fontsize)
{
    // 简化实现 - 暂时为空
}

void OLED_ShowStr(uint8_t x, uint8_t y, char *ch, uint8_t fontsize)
{
    // 简化实现 - 暂时为空
    // 在实际硬件上需要实现字符串显示
}

void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t ch, uint8_t fontsize)
{
    // 简化实现 - 暂时为空
}

void OLED_Allfill(void)
{
    // 简化实现 - 暂时为空
}

void OLED_Set_Position(uint8_t x, uint8_t y)
{
    // 简化实现 - 暂时为空
}

void OLED_Clear(void)
{
    // 简化实现 - 暂时为空
}

void OLED_Display_On(void)
{
    // 简化实现 - 暂时为空
}

void OLED_Display_Off(void)
{
    // 简化实现 - 暂时为空
}

void OLED_Init(void)
{
    // 简化实现 - 暂时为空
    // 在实际硬件上需要实现OLED初始化序列
}
