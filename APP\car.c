#include "car.h"

// 电机初始化
void car_init(void)
{
    // 启动PWM
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);  // 左电机
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_2);  // 右电机
    
    // 初始状态停止
    car_stop();
}

// 停止
void car_stop(void)
{
    // 停止PWM输出
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, 0);
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, 0);
    
    // 方向控制引脚全部拉低
    HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Bin1_GPIO_Port, Bin1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Bin2_GPIO_Port, Bin2_Pin, GPIO_PIN_RESET);
}

// 双电机控制 (核心函数)
void car_run(int left_speed, int right_speed)
{
    // 限制速度范围 -1000 到 1000
    if(left_speed > 1000) left_speed = 1000;
    if(left_speed < -1000) left_speed = -1000;
    if(right_speed > 1000) right_speed = 1000;
    if(right_speed < -1000) right_speed = -1000;
    
    // 左电机控制
    if(left_speed >= 0) {
        // 正转
        HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_RESET);
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, left_speed);
    } else {
        // 反转
        HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_SET);
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, -left_speed);
    }
    
    // 右电机控制
    if(right_speed >= 0) {
        // 正转
        HAL_GPIO_WritePin(Bin1_GPIO_Port, Bin1_Pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(Bin2_GPIO_Port, Bin2_Pin, GPIO_PIN_RESET);
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, right_speed);
    } else {
        // 反转
        HAL_GPIO_WritePin(Bin1_GPIO_Port, Bin1_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(Bin2_GPIO_Port, Bin2_Pin, GPIO_PIN_SET);
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, -right_speed);
    }
}

// 前进
void car_forward(int speed)
{
    car_run(speed, speed);
}

// 后退
void car_backward(int speed)
{
    car_run(-speed, -speed);
}

// 左转
void car_left(int speed)
{
    car_run(-speed, speed);
}

// 右转
void car_right(int speed)
{
    car_run(speed, -speed);
}
