# 🚨 电机紧急调试文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: Alex (工程师)
- **状态**: 紧急调试 - 电机动不了
- **目的**: 快速定位并解决电机不动问题

## 🎯 当前状态

### 问题描述
- **现象**: 电机动不了
- **时机**: 添加Motor_App_Task()空函数后
- **编译**: 编译成功，无错误
- **下载**: 程序正常下载

### 已实施的紧急措施
```c
// 在motor_task中强制电机前进
void motor_task()
{
    // 强制电机前进测试 - 忽略所有模式
    Motor_App_Forward(30);
    
    // 执行电机控制任务
    Motor_App_Task();
}
```

## 🔍 可能原因分析

### 1. 系统模式问题
- **可能**: 系统回到模式0（停止模式）
- **检查**: 观察串口输出是否显示[M0]
- **解决**: 按键切换到模式1或2

### 2. 电机初始化问题
- **可能**: Motor_App_Init()没有正确执行
- **检查**: 确认scheduler_init()中调用了Motor_App_Init()
- **状态**: ✅ 已确认正常

### 3. PWM定时器问题
- **可能**: TIM3定时器没有启动
- **检查**: 确认HAL_TIM_PWM_Start()被调用
- **解决**: 检查main.c中的定时器启动

### 4. GPIO配置问题
- **可能**: 电机控制引脚配置错误
- **检查**: 确认Ain1/Ain2, Bin1/Bin2引脚配置
- **解决**: 检查CubeMX生成的GPIO配置

### 5. 电机驱动硬件问题
- **可能**: 电机驱动板供电或连接问题
- **检查**: 确认电机驱动板供电和连接
- **解决**: 检查硬件连接

## 🔧 紧急排查步骤

### 步骤1: 检查串口输出
```
观察串口输出:
- 如果看到[M0]: 系统在停止模式，需要按键切换
- 如果看到[M1]或[M2]: 模式正常，检查其他问题
- 如果没有输出: 程序可能没有正常运行
```

### 步骤2: 强制电机测试
```c
// 当前已添加的强制测试
void motor_task()
{
    Motor_App_Forward(30);  // 强制前进30速度
    Motor_App_Task();
}
```

### 步骤3: 检查PWM输出
```
使用示波器或万用表检查:
- TIM3_CH1 (左电机PWM): 应该有PWM信号
- TIM3_CH2 (右电机PWM): 应该有PWM信号
- 频率: 通常1-20kHz
- 占空比: 应该对应速度值
```

### 步骤4: 检查方向控制
```
检查GPIO输出:
- Ain1_Pin: 应该有高/低电平变化
- Ain2_Pin: 应该与Ain1相反
- Bin1_Pin: 应该有高/低电平变化  
- Bin2_Pin: 应该与Bin1相反
```

## 🚀 快速解决方案

### 方案1: 检查定时器启动
```c
// 在main.c中确认定时器启动
HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);  // 左电机
HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_2);  // 右电机
```

### 方案2: 强制模式设置
```c
// 在gray_task开头强制设置模式
void gray_task()
{
    sys_mode = 1;  // 强制模式1
    sys_ture = 1;  // 强制启动
    
    // 原有代码...
}
```

### 方案3: 直接PWM测试
```c
// 在motor_task中直接设置PWM
void motor_task()
{
    // 直接设置PWM测试
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, 500);  // 左电机
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, 500);  // 右电机
    
    // 设置方向
    HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Bin1_GPIO_Port, Bin1_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(Bin2_GPIO_Port, Bin2_Pin, GPIO_PIN_RESET);
}
```

### 方案4: 检查电机配置
```c
// 检查电机配置是否正确
void Motor_App_Init(void)
{
    // 确认配置参数
    Motor_Config_Init(&left_motor, &htim3, TIM_CHANNEL_1, 
                      Ain1_GPIO_Port, Ain1_Pin, 
                      Ain2_GPIO_Port, Ain2_Pin, 0, 30);
    Motor_Config_Init(&right_motor, &htim3, TIM_CHANNEL_2, 
                      Bin1_GPIO_Port, Bin1_Pin, 
                      Bin2_GPIO_Port, Bin2_Pin, 0, 30);
}
```

## 📊 硬件检查清单

### 电源检查
- ✅ **主控供电**: 确认STM32供电正常
- ✅ **电机供电**: 确认电机驱动板供电正常
- ✅ **电压等级**: 确认电压等级匹配

### 连接检查
- ✅ **PWM信号**: TIM3_CH1, TIM3_CH2连接正确
- ✅ **方向控制**: Ain1/Ain2, Bin1/Bin2连接正确
- ✅ **电机连接**: 电机与驱动板连接正确
- ✅ **地线**: 确认地线连接良好

### 驱动板检查
- ✅ **使能信号**: 确认驱动板使能
- ✅ **指示灯**: 观察驱动板指示灯状态
- ✅ **发热**: 检查驱动板是否异常发热
- ✅ **保护**: 确认没有触发保护机制

## 🎯 立即行动

### 当前紧急措施
1. **强制前进**: 已在motor_task中添加强制前进
2. **忽略模式**: 忽略系统模式，直接控制电机
3. **固定速度**: 使用固定速度30进行测试

### 下一步操作
1. **重新编译**: 编译当前修改的代码
2. **下载测试**: 下载到目标板测试
3. **观察现象**: 观察电机是否开始转动
4. **检查输出**: 使用万用表检查PWM和GPIO输出

### 如果仍不动
1. **检查硬件**: 重点检查电机驱动板和连接
2. **简化测试**: 使用最简单的PWM输出测试
3. **更换硬件**: 考虑更换电机或驱动板
4. **示波器**: 使用示波器检查信号质量

## 📱 调试输出

### 期望看到的现象
- **电机转动**: 电机应该开始前进
- **PWM信号**: 示波器应该看到PWM信号
- **GPIO变化**: 方向控制引脚应该有正确的电平

### 如果看到的现象
- **无转动**: 检查硬件连接和供电
- **无PWM**: 检查定时器配置和启动
- **无GPIO**: 检查GPIO配置

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**紧急状态**: 🚨 电机不动，正在排查  
**当前措施**: 🔧 强制电机前进测试
