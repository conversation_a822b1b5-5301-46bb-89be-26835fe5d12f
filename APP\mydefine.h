#ifndef MYDEFINE_H
#define MYDEFINE_H

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdlib.h>
#include <main.h>
#include <usart.h>
#include "tim.h"

// UART相关全局变量
extern uint32_t uart_rx_ticks;
extern uint16_t uart_rx_index;
extern uint8_t uart_rx_buffer[128];

// 系统模式相关全局变量
extern uint8_t sys_mode;	//0-STOP 1-(A-B) 2-(ABCD) 3-(ACBD)
extern uint8_t sys_ture;
extern uint8_t pre_mode;    // 预选模式

// 灰度传感器相关全局变量
extern uint8_t gray_buff[8];

// MPU6050相关全局变量
extern float Pitch, Roll, Yaw;
#endif
