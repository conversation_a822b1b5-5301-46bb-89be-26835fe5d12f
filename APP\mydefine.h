#ifndef MYDEFINE_H
#define MYDEFINE_H

// 标准C库头文件
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdlib.h>
#include <math.h>

// STM32 HAL库头文件
#include "main.h"
#include "stm32f4xx_hal.h"
#include "usart.h"
#include "tim.h"

// 应用模块头文件
#include "scheduler.h"
#include "key.h"
#include "my_uart.h"
#include "Gray.h"
#include "car.h"
#include "mpu6050_app.h"

// UART相关全局变量
extern uint32_t uart_rx_ticks;
extern uint16_t uart_rx_index;
extern uint8_t uart_rx_buffer[128];

// 系统模式相关全局变量
extern uint8_t sys_mode;	//0-STOP 1-(A-B) 2-(ABCD) 3-(ACBD)
extern uint8_t sys_ture;
extern uint8_t pre_mode;    // 预选模式

// 灰度传感器相关全局变量
extern uint8_t gray_buff[8];

// MPU6050相关全局变量
extern float Pitch, Roll, Yaw;
#endif
