#include "mydefine.h"

uint8_t sys_mode;	//当前执行的模式：0-停止 1-前进(A-B) 2-一圈(ABCD) 3-斜圈(ACBD)
uint8_t sys_ture;	//启动标志：0-未启动 1-启动
uint8_t pre_mode;	//预选模式：按钮0/1选择，按钮2确认

uint8_t key_val,key_old,key_down,key_up;

uint8_t Key_Read()
{
	uint8_t temp = 0;
	if(HAL_GPIO_ReadPin(key2_GPIO_Port,key2_Pin)==GPIO_PIN_RESET)
		temp = 1;
	if(HAL_GPIO_ReadPin(key1_GPIO_Port,key1_Pin)==GPIO_PIN_RESET)
		temp = 2;
	if(HAL_GPIO_ReadPin(key0_GPIO_Port,key0_Pin)==GPIO_PIN_RESET)
		temp = 3;
	return temp;
}

void key_task(void)
{
		key_val = Key_Read();
		key_down = key_val&(key_val ^ key_old);
		key_up = ~key_val&(key_val ^ key_old);
		key_old = key_val;

		switch(key_down)
		{
				case 3:  // KEY0按下 - 预选模式正序切换 (0→1→2→3→0)
					pre_mode = (pre_mode+1)%4;
				break;
				case 2:  // KEY1按下 - 预选模式倒序切换 (3→2→1→0→3)
					pre_mode = (pre_mode == 0) ? 3 : (pre_mode - 1);
				break;
				case 1:  // KEY2按下 - 确认执行预选模式
					sys_mode = pre_mode;  // 将预选模式设为当前执行模式
					sys_ture = 1;         // 设置启动标志
				break;
		}
}
