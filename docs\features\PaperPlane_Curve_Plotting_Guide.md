# 📈 纸飞机调试工具曲线绘制指南

## 📊 功能概述

实现了纸飞机调试工具的TEXT协议，可以实时绘制双电机速度曲线：
- **RPM曲线**: 显示左右电机的转速变化
- **线速度曲线**: 显示左右电机的线性速度变化
- **实时更新**: 每150ms更新一次数据点

## 🔧 技术实现

### **TEXT协议宏定义**
```c
// TEXT协议宏定义 - 纸飞机调试工具
#define PRINT(window, fmt, ...) my_printf(&huart1, "{" #window "}" fmt "\n", ##__VA_ARGS__)
```

### **曲线绘制代码**
```c
// 纸飞机调试工具 - 速度曲线绘制
PRINT(plotter, "%.2f,%.2f", fabs(rpm_l), fabs(rpm_r));  // RPM曲线
PRINT(speed, "%.2f,%.2f", fabs(speed_l), fabs(speed_r)); // 线速度曲线
```

## 📈 输出数据格式

### **RPM曲线窗口 (plotter)**
```
{plotter}2.50,2.30
{plotter}2.45,2.35
{plotter}2.60,2.25
```

### **线速度曲线窗口 (speed)**
```
{speed}0.82,0.75
{speed}0.80,0.77
{speed}0.85,0.73
```

### **原始数据输出 (保留)**
```
L:F2.5RPM 0.8cm/s | R:F2.3RPM 0.7cm/s
```

## 🎯 纸飞机调试工具设置

### **窗口配置**
1. **plotter窗口**: 显示RPM曲线
   - 曲线1: 左电机RPM (红色)
   - 曲线2: 右电机RPM (蓝色)

2. **speed窗口**: 显示线速度曲线
   - 曲线1: 左电机线速度 (绿色)
   - 曲线2: 右电机线速度 (橙色)

### **串口设置**
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **协议**: TEXT协议

## 📊 数据特点

### **数据精度**
- **RPM**: 保留2位小数 (如: 2.50)
- **线速度**: 保留2位小数 (如: 0.82)
- **更新频率**: 150ms (约6.7Hz)

### **数据范围**
- **RPM范围**: 0-50 RPM (典型工作范围)
- **线速度范围**: 0-16 cm/s (基于6.5cm轮径)
- **数据稳定性**: 使用fabs()确保正值

## 🔍 曲线分析

### **正常运行特征**
```
RPM曲线:
- 左右电机RPM基本一致
- 数值在1-5 RPM范围内
- 曲线相对平稳

线速度曲线:
- 与RPM曲线成正比关系
- 数值在0.3-1.6 cm/s范围内
- 反映实际移动速度
```

### **异常情况识别**
```
速度差异过大:
- 左右曲线分离明显
- 可能存在机械问题

速度波动剧烈:
- 曲线锯齿状
- 可能存在编码器问题

速度为零:
- 曲线平直在零线
- 电机或编码器故障
```

## 🎮 使用方法

### **1. 硬件连接**
- 串口连接到纸飞机调试工具
- 确保波特率设置为115200
- 确保电机和编码器正常工作

### **2. 软件设置**
- 打开纸飞机调试工具
- 创建两个绘图窗口: plotter 和 speed
- 设置TEXT协议解析
- 开始数据接收

### **3. 观察曲线**
- **plotter窗口**: 观察RPM变化趋势
- **speed窗口**: 观察线速度变化
- **实时监控**: 观察左右电机一致性

## 📈 应用场景

### **调试应用**
- **电机性能测试**: 观察电机响应特性
- **控制算法调试**: 验证控制效果
- **机械问题诊断**: 发现机械异常
- **系统优化**: 优化控制参数

### **监控应用**
- **实时监控**: 长期运行状态监控
- **性能分析**: 分析电机性能数据
- **故障预警**: 及时发现异常情况
- **数据记录**: 保存历史运行数据

## 🔧 自定义配置

### **修改窗口名称**
```c
// 可以修改窗口名称
PRINT(motor_rpm, "%.2f,%.2f", fabs(rpm_l), fabs(rpm_r));
PRINT(motor_speed, "%.2f,%.2f", fabs(speed_l), fabs(speed_r));
```

### **修改数据精度**
```c
// 修改小数位数
PRINT(plotter, "%.1f,%.1f", fabs(rpm_l), fabs(rpm_r));  // 1位小数
PRINT(plotter, "%.3f,%.3f", fabs(rpm_l), fabs(rpm_r));  // 3位小数
```

### **添加更多曲线**
```c
// 添加编码器原始数据曲线
PRINT(encoder, "%d,%d", count_l, count_r);

// 添加PWM输出曲线
PRINT(pwm, "%d,%d", pwm_left, pwm_right);
```

## 📊 数据解读

### **RPM曲线解读**
```
稳定运行: 左右曲线重合，数值稳定
加速过程: 曲线上升趋势
减速过程: 曲线下降趋势
负载变化: 曲线波动反映负载变化
```

### **线速度曲线解读**
```
直线行驶: 左右曲线一致
转弯行驶: 左右曲线分离
停止状态: 曲线归零
异常状态: 曲线异常波动
```

## ⚠️ 注意事项

### **数据传输**
- 确保串口连接稳定
- 避免数据传输中断
- 注意波特率匹配

### **曲线显示**
- 设置合适的Y轴范围
- 调整时间窗口长度
- 选择合适的颜色区分

### **性能影响**
- 增加了串口数据量
- 可能影响实时性
- 可根据需要调整更新频率

## 🔍 故障排除

### **无曲线显示**
1. 检查串口连接
2. 确认波特率设置
3. 验证TEXT协议格式
4. 检查窗口名称匹配

### **曲线异常**
1. 检查编码器连接
2. 验证电机工作状态
3. 确认数据计算正确
4. 检查机械安装

### **数据不更新**
1. 确认系统正常运行
2. 检查调度器工作
3. 验证串口发送功能
4. 确认纸飞机工具接收

## 📈 扩展功能

### **可添加的曲线**
```c
// 电机电流曲线
PRINT(current, "%.2f,%.2f", current_l, current_r);

// 电机温度曲线
PRINT(temperature, "%.1f,%.1f", temp_l, temp_r);

// 控制误差曲线
PRINT(error, "%.2f,%.2f", error_l, error_r);

// 系统状态曲线
PRINT(status, "%d,%d", status_l, status_r);
```

### **数据处理**
- 数据滤波处理
- 移动平均计算
- 峰值检测
- 趋势分析

## 📞 技术支持

使用中如遇问题：
1. 确认硬件连接正确
2. 验证软件配置
3. 检查数据格式
4. 参考故障排除指南

---

**创建时间**: 2025-06-20  
**协议**: TEXT协议  
**工具**: 纸飞机调试工具  
**状态**: ✅ 已实现
