# ✅ MPU6050功能启用完成文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 已启用
- **目的**: 启用MPU6050完整功能，实现实时姿态监测

## 🎯 启用内容

### 1. 初始化启用
```c
void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
    // 初始化MPU6050 ✅ 已启用
    Mpu6050_Init();
}
```

### 2. 任务执行启用
```c
void mpu_task()
{
    // 执行MPU6050数据读取和处理 ✅ 已启用
    Mpu6050_Task();
}
```

### 3. 头文件包含启用
```c
#include "mpu6050_app.h"  // ✅ 已启用
```

### 4. 数据输出启用
```c
// 简洁的左右轮速度输出 + MPU6050姿态角 ✅ 已启用
my_printf(&huart1, "L:%.1f R:%.1f | P:%.1f R:%.1f Y:%.1f\r\n", 
          rpm_l, -rpm_r, Pitch, Roll, Yaw);
```

## 📊 功能状态

### 完全启用的功能
- ✅ **MPU6050初始化**: 系统启动时自动初始化
- ✅ **实时数据读取**: 20ms周期读取姿态数据
- ✅ **姿态解算**: 实时计算俯仰角、横滚角、偏航角
- ✅ **串口输出**: 完整的电机+姿态数据显示
- ✅ **任务调度**: 集成到主任务调度器

### 输出格式
```
L:25.3 R:-18.7 | P:12.5 R:-3.2 Y:45.8
L:30.1 R:22.4 | P:10.1 R:-2.8 Y:46.2
L:0.0 R:0.0 | P:0.5 R:0.1 Y:46.2
```

### 数据含义
- **L**: 左轮速度 (RPM)
- **R**: 右轮速度 (RPM，已反向)
- **P**: 俯仰角 Pitch (度)
- **R**: 横滚角 Roll (度)
- **Y**: 偏航角 Yaw (度，连续值)

## 🔧 技术规格

### MPU6050配置
- **陀螺仪量程**: ±2000dps
- **加速度计量程**: ±2g
- **采样率**: 50Hz
- **低通滤波**: 42Hz
- **I2C频率**: ~200KHz

### 任务调度
- **MPU6050任务**: 20ms周期 (50Hz)
- **串口输出**: 50ms周期 (20Hz)
- **数据同步**: 姿态数据与电机数据同步输出

### 姿态解算
- **俯仰角**: 基于加速度计atan2计算
- **横滚角**: 基于加速度计atan2计算
- **偏航角**: 基于陀螺仪Z轴积分计算
- **连续偏航角**: 解决±180°跳变问题

## 🎮 应用场景

### 1. 实时姿态监控
```
L:0.0 R:0.0 | P:0.5 R:0.1 Y:46.2    // 静止，水平
L:25.0 R:-25.0 | P:15.3 R:0.2 Y:46.8  // 上坡，后仰15.3°
L:30.0 R:-10.0 | P:1.5 R:5.2 Y:52.3   // 右转，右倾5.2°
```

### 2. 运动状态分析
- **直线行驶**: 左右轮速度对称，偏航角稳定
- **转弯运动**: 速度差异，横滚角和偏航角变化
- **坡道行驶**: 俯仰角明显变化
- **颠簸路面**: 姿态角频繁变化

### 3. 循迹算法优化
- **方向修正**: 基于偏航角的方向控制
- **稳定性监控**: 检测过度倾斜
- **路面适应**: 根据俯仰角调整速度

## ⚠️ 注意事项

### 1. 硬件连接
- **确认连接**: PE14(SCL), PE15(SDA)
- **电源供电**: 3.3V稳定供电
- **上拉电阻**: 建议添加4.7kΩ上拉电阻

### 2. 数据特性
- **俯仰角精度**: ±1°
- **横滚角精度**: ±1°
- **偏航角**: 会有累积误差，建议定期校准

### 3. 环境影响
- **振动影响**: 高频振动可能影响精度
- **温度影响**: 温度变化可能影响零点
- **磁场干扰**: 偏航角可能受磁场影响

## 🔄 调试建议

### 1. 基础检查
```c
// 检查设备ID
uint8_t device_id = MPU_Read_Byte(MPU_DEVICE_ID_REG);
// 应该返回0x68
```

### 2. 数据验证
- **静止测试**: 静止时俯仰角和横滚角应接近0
- **倾斜测试**: 手动倾斜验证角度计算
- **转动测试**: 转动验证偏航角变化

### 3. 性能监控
- **更新频率**: 确认50Hz数据更新
- **数据连续性**: 检查数据是否连续无跳变
- **系统负载**: 监控MPU6050任务对系统的影响

## 🎯 成功指标

### 启用成功标志
- ✅ **编译通过**: 无编译错误和警告
- ✅ **初始化成功**: MPU6050正确初始化
- ✅ **数据输出**: 串口显示完整的姿态数据
- ✅ **实时更新**: 姿态角实时变化

### 功能验证
- ✅ **静止状态**: P≈0°, R≈0°, Y稳定
- ✅ **前后倾斜**: P角度正确变化
- ✅ **左右倾斜**: R角度正确变化
- ✅ **水平转动**: Y角度连续变化

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**功能状态**: ✅ 完全启用  
**技术支持**: STM32循迹小车开发团队
