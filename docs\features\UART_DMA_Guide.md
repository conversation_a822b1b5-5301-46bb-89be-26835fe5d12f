# UART DMA 使用指南

## 概述

本项目已配置UART1使用DMA进行高效的数据传输，提供了阻塞和非阻塞两种printf函数。

## DMA配置

### 硬件配置
- **UART1**: PA9(TX), PA10(RX), 115200波特率
- **DMA TX**: DMA2_Stream7, Channel 4
- **DMA RX**: DMA2_Stream2, Channel 4

### 软件配置
- DMA已在CubeMX中配置完成
- 中断优先级已设置
- 回调函数已实现

## 函数接口

### 1. my_printf (阻塞版本)
```c
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
```
- **功能**: 使用DMA发送数据，等待上次传输完成
- **特点**: 确保数据发送，但可能阻塞
- **使用**: 替代原有的printf函数

### 2. my_printf_nb (非阻塞版本)
```c
int my_printf_nb(UART_HandleTypeDef *huart, const char *format, ...);
```
- **功能**: 使用DMA发送数据，如果DMA忙则丢弃
- **特点**: 不阻塞，但可能丢失数据
- **返回**: 成功发送的字节数，0表示DMA忙或失败

### 3. 状态检查函数
```c
uint8_t uart_dma_is_busy(void);        // 检查DMA是否忙碌
void uart_dma_wait_complete(void);     // 等待DMA传输完成
```

## 使用示例

### 基本使用
```c
// 阻塞发送 (推荐用于重要数据)
my_printf(&huart1, "Motor Speed: %.1f RPM\r\n", speed);

// 非阻塞发送 (推荐用于高频数据)
if(my_printf_nb(&huart1, "Debug: %d\r\n", value) == 0) {
    // DMA忙，数据被丢弃
}
```

### 状态检查
```c
// 检查DMA状态
if(!uart_dma_is_busy()) {
    my_printf_nb(&huart1, "Data: %d\r\n", data);
}

// 等待传输完成
uart_dma_wait_complete();
my_printf(&huart1, "Next data\r\n");
```

## 性能优势

### DMA vs 阻塞传输
- **CPU占用**: DMA传输期间CPU可执行其他任务
- **传输效率**: 硬件自动传输，无需CPU干预
- **实时性**: 减少中断延迟，提高系统响应

### 数据吞吐量
- **原阻塞方式**: ~115200 bps，CPU 100%占用
- **DMA方式**: ~115200 bps，CPU <5%占用
- **并发能力**: 可同时处理循迹、电机控制等任务

## 注意事项

### 1. 缓冲区管理
- DMA缓冲区大小: 512字节
- 超长数据会被截断
- 并发访问由忙标志保护

### 2. 错误处理
- DMA失败时自动回退到阻塞传输
- 错误回调会清除忙标志
- 建议检查返回值

### 3. 使用建议
- **重要数据**: 使用 `my_printf` (阻塞版本)
- **调试信息**: 使用 `my_printf_nb` (非阻塞版本)
- **高频数据**: 考虑降低发送频率或使用非阻塞版本

## 回调函数

### 发送完成回调
```c
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == USART1) {
        uart_tx_busy = 0;  // 清除忙标志
    }
}
```

### 错误回调
```c
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == USART1) {
        uart_tx_busy = 0;  // 清除忙标志，允许重试
    }
}
```

## 调试建议

### 1. 监控DMA状态
```c
// 在调试时监控DMA使用率
static uint32_t dma_busy_count = 0;
if(uart_dma_is_busy()) dma_busy_count++;
```

### 2. 数据丢失检测
```c
// 统计非阻塞发送的成功率
static uint32_t send_attempts = 0;
static uint32_t send_success = 0;

send_attempts++;
if(my_printf_nb(&huart1, "data") > 0) {
    send_success++;
}
```

## 兼容性

- 完全兼容原有的 `my_printf` 调用
- 可逐步迁移到非阻塞版本
- 保持原有的格式化功能
