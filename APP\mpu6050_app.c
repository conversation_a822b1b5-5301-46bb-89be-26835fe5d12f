#include "mpu6050_app.h"
#include "MPU6050/mpu6050.h"
#include "MPU6050/inv_mpu.h"
#include <math.h>

// 姿态角数据
float Pitch, Roll, Yaw;

// 方向控制数据
static float target_yaw = 0.0f;
static uint8_t direction_initialized = 0;

void Mpu6050_Init(void)
{
    // 真实MPU6050 DMP初始化
    mpu_dmp_init();               // 初始化DMP (数字运动处理器)
    Direction_Control_Init();      // 初始化方向控制
}

void Mpu6050_Task(void)
{
    // 真实MPU6050 DMP数据读取
    if(mpu_dmp_get_data(&Pitch, &Roll, &Yaw) == 0) {
        // 成功读取DMP处理后的姿态角数据
        Yaw = convert_to_continuous_yaw(Yaw);  // 转换为连续角度
        Direction_Control_Update();            // 更新方向控制
    }
    // 如果读取失败，保持上次的数据不变
}

// 方向控制初始化
void Direction_Control_Init(void)
{
    target_yaw = 0.0f;
    direction_initialized = 0;
}

// 方向控制更新
void Direction_Control_Update(void)
{
    // 首次运行时设置目标方向
    if(!direction_initialized) {
        target_yaw = Yaw;
        direction_initialized = 1;
    }
}

// 获取偏航角误差
float Get_Yaw_Error(void)
{
    if(!direction_initialized) return 0.0f;
    
    float yaw_error = target_yaw - Yaw;
    
    // 处理±180°跳变
    if(yaw_error > 180.0f) yaw_error -= 360.0f;
    if(yaw_error < -180.0f) yaw_error += 360.0f;
    
    return yaw_error;
}

// 获取方向修正值
int8_t Get_Direction_Correction(void)
{
    float yaw_error = Get_Yaw_Error();
    
    // 方向容差：小于2度认为方向正确
    if(fabs(yaw_error) <= 2.0f) {
        return 0;
    }
    
    // 计算方向修正值
    int8_t correction = (int8_t)(yaw_error * 0.3f);
    
    // 限制修正值范围
    if(correction > 15) correction = 15;
    if(correction < -15) correction = -15;
    
    return correction;
}

// 设置目标方向
void Set_Target_Direction(float target)
{
    target_yaw = target;
    direction_initialized = 1;
}

// 重置目标方向为当前方向
void Reset_Target_Direction(void)
{
    target_yaw = Yaw;
    direction_initialized = 1;
}

// 连续偏航角转换 (处理±180°跳变)
float convert_to_continuous_yaw(float current_yaw)
{
    static uint8_t is_initialized = 0;
    static float last_yaw = 0.0f;
    static int revolution_count = 0;

    if(!is_initialized) {
        last_yaw = current_yaw;
        is_initialized = 1;
        return current_yaw;
    }

    float delta = current_yaw - last_yaw;

    // 检测±180°跳变
    if(delta > 180.0f) {
        revolution_count--;  // 逆时针跳变
    } else if(delta < -180.0f) {
        revolution_count++;  // 顺时针跳变
    }

    last_yaw = current_yaw;

    // 返回连续的偏航角 (可以超过±180°)
    return current_yaw + revolution_count * 360.0f;
}
