# 🔊 声光控制功能实现

## 📊 功能概述

实现了基于PF11引脚的声光控制功能，当小车从空白区域进入黑线时自动触发声光提示。

## 🎯 触发条件

### **检测逻辑**
- **触发条件**: 从无黑线状态 → 检测到黑线状态
- **检测方式**: 基于6路灰度传感器的综合判断
- **触发频率**: 每次状态变化触发一次

### **状态定义**
```c
// 无黑线状态
sensor_sum == 0  // 6路传感器都没有检测到黑线

// 有黑线状态  
sensor_sum > 0   // 至少一个传感器检测到黑线
```

## 🔧 技术实现

### **硬件配置**
- **控制引脚**: PF11 (FMD_LED_Pin)
- **GPIO端口**: GPIOF (FMD_LED_GPIO_Port)
- **控制方式**: 数字输出，高电平触发

### **软件实现**
```c
// 黑线检测和声光控制 (PF11)
static uint8_t last_line_detected = 0;  // 上次是否检测到线
uint8_t current_line_detected = (sensor_sum > 0) ? 1 : 0;  // 当前是否检测到线

// 检测从无线到有线的状态变化
if(!last_line_detected && current_line_detected) {
    // 从空白区域进入黑线，触发声光控制
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_SET);   // PF11置高
    HAL_Delay(100);  // 持续100ms
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_RESET); // PF11置低
    
    my_printf(&huart1," [BEEP]");  // 串口提示
}

// 更新上次状态
last_line_detected = current_line_detected;
```

## 📊 工作流程

### **状态检测流程**
```
1. 读取6路灰度传感器 → Gray_get(gray_buff)
2. 计算传感器总和 → sensor_sum
3. 判断当前状态 → current_line_detected = (sensor_sum > 0)
4. 对比上次状态 → if(!last_line_detected && current_line_detected)
5. 触发声光控制 → PF11高电平100ms
6. 更新状态记录 → last_line_detected = current_line_detected
```

### **触发时序**
```
时间轴: ----[无线]----[检测到线]----[继续有线]----[无线]----
状态:     0           1            1           0
触发:               ↑ BEEP                   (不触发)
```

## 🎮 使用场景

### **典型应用场景**
1. **起始检测**: 小车放置在起点，开始循迹时触发
2. **重新找线**: 丢线后重新找到线时触发
3. **路径切换**: 从一条线切换到另一条线时触发
4. **调试验证**: 手动测试传感器响应时触发

### **不会触发的情况**
- **持续有线**: 在黑线上正常循迹时不触发
- **持续无线**: 在空白区域时不触发
- **线到线**: 从一个黑线位置移动到另一个黑线位置时不触发

## 📈 串口监控

### **输出格式**
```
[TRACK] X110011X Pos:3500 PID:0.0 L:35 R:35
[TRACK] X000000X [BEEP]  // 检测到黑线，触发声光
[TRACK] X110011X Pos:3500 PID:0.0 L:35 R:35
[TRACK] X111111X NO_LINE  // 丢线，无触发
[TRACK] X110011X Pos:3500 [BEEP]  // 重新找到线，再次触发
```

### **监控信息**
- **[BEEP]**: 表示声光控制被触发
- **传感器状态**: X110011X显示6路传感器状态
- **位置信息**: Pos:3500显示线位置

## ⚙️ 参数配置

### **声光持续时间**
```c
HAL_Delay(100);  // 100ms持续时间

// 可调整参数:
HAL_Delay(50);   // 50ms - 短促提示
HAL_Delay(200);  // 200ms - 长时间提示
HAL_Delay(500);  // 500ms - 明显提示
```

### **触发灵敏度**
```c
// 当前: 任意传感器检测到黑线即触发
uint8_t current_line_detected = (sensor_sum > 0) ? 1 : 0;

// 可调整为更严格的条件:
uint8_t current_line_detected = (sensor_sum >= 2000) ? 1 : 0;  // 至少2个传感器
uint8_t current_line_detected = (sensor_sum >= 3000) ? 1 : 0;  // 至少3个传感器
```

## 🔍 调试和测试

### **测试方法**
1. **手动测试**: 手动将传感器从白色表面移到黑线上
2. **循迹测试**: 观察小车开始循迹时是否触发
3. **丢线测试**: 故意让小车丢线，然后重新找线
4. **串口监控**: 通过串口观察[BEEP]提示

### **调试输出**
```c
// 可以添加更详细的调试信息
my_printf(&huart1," Last:%d Curr:%d Sum:%d", 
          last_line_detected, current_line_detected, sensor_sum);
```

### **故障排除**
```c
// 如果不触发，检查:
1. PF11引脚配置是否正确
2. 传感器是否正常工作
3. sensor_sum计算是否正确
4. 状态变化逻辑是否正确

// 如果频繁触发，检查:
1. 传感器是否有噪声
2. 阈值设置是否合适
3. 状态记录是否正确更新
```

## 📊 性能特点

### **响应特性**
- **检测频率**: 30ms (gray_task执行频率)
- **响应延迟**: <30ms (一个任务周期内)
- **触发精度**: 基于6路传感器综合判断
- **防抖动**: 通过状态记录避免重复触发

### **资源占用**
- **内存**: 1字节静态变量 (last_line_detected)
- **CPU**: 最小开销 (简单状态比较)
- **GPIO**: 1个输出引脚 (PF11)
- **延时**: 100ms阻塞延时 (触发时)

## ⚠️ 注意事项

### **延时影响**
- **阻塞延时**: HAL_Delay(100)会阻塞任务执行100ms
- **循迹影响**: 触发时会暂停循迹控制100ms
- **优化建议**: 可以考虑使用非阻塞方式

### **硬件连接**
- **确认PF11**: 确保PF11连接到声光设备
- **电流能力**: 确保GPIO输出电流足够驱动设备
- **电压匹配**: 确保3.3V输出电压适合设备

### **软件集成**
- **任务优先级**: 声光控制在gray_task中执行
- **状态一致性**: 确保状态变量正确更新
- **异常处理**: 考虑传感器异常时的处理

## 🔧 扩展功能

### **可选增强**
```c
// 1. 非阻塞声光控制
static uint32_t beep_start_time = 0;
static uint8_t beep_active = 0;

if(!last_line_detected && current_line_detected) {
    beep_start_time = HAL_GetTick();
    beep_active = 1;
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_SET);
}

if(beep_active && (HAL_GetTick() - beep_start_time > 100)) {
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_RESET);
    beep_active = 0;
}

// 2. 多种提示模式
typedef enum {
    BEEP_SHORT = 50,
    BEEP_NORMAL = 100,
    BEEP_LONG = 200
} BeepDuration;

// 3. 计数功能
static uint16_t line_detect_count = 0;
if(!last_line_detected && current_line_detected) {
    line_detect_count++;
    my_printf(&huart1," [BEEP:%d]", line_detect_count);
}
```

---

**实现完成时间**: 2025-06-20  
**功能**: PF11声光控制，黑线检测触发  
**特点**: 状态变化检测 + 自动触发 + 串口监控  
**状态**: ✅ 已实现
