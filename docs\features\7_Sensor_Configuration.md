# 🔧 7路灰度传感器配置

## 📊 配置概述

恢复为7路灰度传感器配置，忽略有问题的P_1，使用P_2到P_8进行循迹：
- **忽略传感器**: P_1 (有硬件问题)
- **有效传感器**: P_2, P_3, P_4, P_5, P_6, P_7, P_8
- **传感器数量**: 7个有效传感器

## 🎯 传感器布局

### **传感器排列**
```
[X] [1] [2] [3] [4] [5] [6] [7]
P_1 P_2 P_3 P_4 P_5 P_6 P_7 P_8
忽略              有效传感器
```

### **权重分配**
```
传感器:  P_2  P_3  P_4  P_5  P_6  P_7  P_8
权重:    1000 2000 3000 4000 5000 6000 7000
位置:    左侧      中心      右侧
```

### **中心计算**
```c
// 7路传感器的中心位置
center = (1000 + 7000) / 2 = 4000

// PID目标位置
pid_line.target = 4000.0f;
```

## ⚙️ 代码实现

### **Gray.c修改**
```c
void Gray_get(uint8_t *gray_buf)
{
    // 7路灰度传感器读取 (忽略P_1，使用P_2到P_8)
    gray_buf[0] = 1;  // P_1忽略，设为1 (白线状态)
    gray_buf[1] = HAL_GPIO_ReadPin(P_2_GPIO_Port, P_2_Pin);   // 传感器2
    gray_buf[2] = HAL_GPIO_ReadPin(P_3_GPIO_Port, P_3_Pin);   // 传感器3
    gray_buf[3] = HAL_GPIO_ReadPin(P_4_GPIO_Port, P_4_Pin);   // 传感器4
    gray_buf[4] = HAL_GPIO_ReadPin(P_5_GPIO_Port, P_5_Pin);   // 传感器5
    gray_buf[5] = HAL_GPIO_ReadPin(P_6_GPIO_Port, P_6_Pin);   // 传感器6
    gray_buf[6] = HAL_GPIO_ReadPin(P_7_GPIO_Port, P_7_Pin);   // 传感器7
    gray_buf[7] = HAL_GPIO_ReadPin(P_8_GPIO_Port, P_8_Pin);   // 传感器8
}
```

### **循迹算法修改**
```c
// 计算线位置 (加权平均，使用7路传感器)
for(int i = 1; i < 8; i++) {  // 使用P_2到P_8 (索引1-7)
    if(gray_buff[i] == 0) {  // 0表示检测到黑线
        line_position += i * 1000;  // 位置权重
        sensor_sum += 1000;
    }
}
```

### **PID目标调整**
```c
// 循迹PID参数：目标位置4000 (7路传感器中心)
PID_Init(&pid_line, 0.02f, 0.001f, 0.005f, 30.0f, -30.0f);
pid_line.target = 4000.0f;  // 7路传感器阵列中心
```

## 📊 串口输出格式

### **新的显示格式**
```
[TRACK] X1100111 Pos:4000 PID:0.0 L:35 R:35
[TRACK] X0111111 Pos:2000 PID:-40.0 L:75 R:-5
[TRACK] X1111100 Pos:6500 PID:50.0 L:-15 R:85
```

### **格式说明**
- **X1100111**: 7路传感器状态，第一位X表示忽略的P_1
- **Pos:4000**: 线位置 (1000-7000范围)
- **PID:0.0**: PID输出 (目标4000时为0)

## 🎮 控制效果

### **位置检测范围**
```c
// 7路传感器的位置范围
最左: P_2检测 → Pos:1000
左偏: P_3检测 → Pos:2000  
左中: P_4检测 → Pos:3000
中心: P_5检测 → Pos:4000
右中: P_6检测 → Pos:5000
右偏: P_7检测 → Pos:6000
最右: P_8检测 → Pos:7000
```

### **PID控制响应**
```c
// 完美居中 (P_5检测到)
Pos:4000 PID:0.0 L:35 R:35 → 直线前进

// 轻微左偏 (P_4检测到)
Pos:3000 PID:-20.0 L:55 R:15 → 右转修正

// 轻微右偏 (P_6检测到)  
Pos:5000 PID:20.0 L:15 R:55 → 左转修正

// 明显左偏 (P_3检测到)
Pos:2000 PID:-40.0 L:75 R:-5 → 强力右转

// 明显右偏 (P_7检测到)
Pos:6000 PID:40.0 L:-5 R:75 → 强力左转
```

## 📈 相比6路配置的优势

### **检测范围更广**
- ✅ **更宽检测**: 7路传感器覆盖更宽范围
- ✅ **边缘检测**: P_8提供最右侧检测能力
- ✅ **精度提升**: 更多传感器提供更高精度

### **控制性能提升**
- ✅ **转向范围**: 支持更大的转向角度
- ✅ **响应能力**: 对急弯的响应更好
- ✅ **稳定性**: 更多传感器提供更稳定的检测

## 🔧 参数优化

### **PID参数保持**
```c
// 当前参数适合7路配置
Kp = 0.02   // 比例系数
Ki = 0.001  // 积分系数  
Kd = 0.005  // 微分系数
输出范围 = ±30
目标位置 = 4000
```

### **如需调整**
```c
// 如果响应不够
PID_Init(&pid_line, 0.025f, 0.001f, 0.005f, 30.0f, -30.0f);

// 如果有振荡
PID_Init(&pid_line, 0.015f, 0.001f, 0.008f, 30.0f, -30.0f);
```

## 🎯 使用效果

### **循迹表现**
- **中心检测**: 更准确的中心位置检测
- **边缘处理**: 更好的边缘和急弯处理
- **稳定控制**: 7路传感器提供更稳定的数据
- **精确定位**: 1000级精度，7个检测点

### **适用场景**
- **复杂赛道**: 包含急弯的复杂赛道
- **宽线循迹**: 较宽黑线的循迹应用
- **高精度要求**: 需要精确控制的场合
- **边缘检测**: 需要检测线边缘的应用

## 📊 性能对比

### **检测精度**
```
8路: 0-7000 (8个位置，但P_1有问题)
6路: 1000-6000 (6个位置，平衡但范围小)
7路: 1000-7000 (7个位置，范围大且可用) ✅
```

### **中心精度**
```
8路: 3500 (理论中心，但P_1有问题)
6路: 3500 (6路的真实中心)
7路: 4000 (7路的真实中心) ✅
```

### **检测范围**
```
8路: 最宽 (但P_1不可用)
6路: 中等 (缺少边缘检测)
7路: 较宽 (实用的最大范围) ✅
```

## ⚠️ 注意事项

### **检测范围**
- **有效范围**: 1000-7000
- **中心位置**: 4000
- **左侧盲区**: 最左端P_1无检测

### **赛道要求**
- **线宽适中**: 不要太窄，确保7路传感器能检测
- **居中放置**: 确保传感器阵列居中对准
- **稳定光照**: 7路传感器需要一致的光照条件

## 🔍 故障排除

### **如果偏向右侧**
```c
// P_1被忽略，可能导致轻微右偏
// 可以通过调整目标位置补偿
pid_line.target = 3800.0f;  // 稍微左偏目标
```

### **如果检测不到线**
```c
// 可能线太细或太偏左
// 调整传感器位置或检查线宽
// 确认线在7路传感器覆盖范围内
```

---

**配置完成时间**: 2025-06-20  
**配置类型**: 7路传感器配置  
**特点**: 忽略P_1 + 最大可用范围 + 高精度控制  
**状态**: ✅ 已实现
