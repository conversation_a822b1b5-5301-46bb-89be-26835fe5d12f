#ifndef __MOTOR_APP_H__
#define __MOTOR_APP_H__

#include "MyDefine.h"

// 初始化和任务函数
void Motor_App_Init(void);
void Motor_App_Task(void);

// 基本控制函数 (应用层接口)
void Motor_App_Stop(void);
void Motor_App_Forward(int8_t speed);
void Motor_App_Backward(int8_t speed);
void Motor_App_Turn_Left(int8_t speed);
void Motor_App_Turn_Right(int8_t speed);
void Motor_App_Set_Speed(int8_t left_speed, int8_t right_speed);

#endif
