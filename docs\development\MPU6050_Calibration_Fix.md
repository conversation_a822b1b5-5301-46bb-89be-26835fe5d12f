# 🔧 MPU6050校准优化完成文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 已完成
- **目的**: 解决MPU6050数据异常，实施校准和滤波优化

## 🚨 问题分析

### 观察到的异常数据
```
L:0.0 R:0.0 | P:1.2 R:8.3 Y:3.2     // 静止时Roll应接近0°
L:0.0 R:0.0 | P:-39.1 R:-50.7 Y:35.9 // 数据剧烈波动
L:0.0 R:0.0 | P:27.8 R:-27.2 Y:5.2   // 大幅度跳变
```

### 问题诊断
1. **零点偏移**: 静止时Roll角度显示8-10°偏移
2. **数据不稳定**: 姿态角剧烈波动(-60°到+40°)
3. **传感器漂移**: 长时间运行数据持续漂移
4. **噪声干扰**: 高频噪声影响数据质量

## 🔧 解决方案

### 1. 自动校准系统

#### 校准变量
```c
// 校准偏移量
static float accel_offset_x = 0.0f;
static float accel_offset_y = 0.0f;
static float accel_offset_z = 0.0f;
static short gyro_offset_x = 0;
static short gyro_offset_y = 0;
static short gyro_offset_z = 0;
static uint8_t calibration_done = 0;
```

#### 校准函数
```c
void mpu6050_calibrate(void)
{
    // 200次采样校准
    for(int i = 0; i < 200; i++) {
        // 读取加速度计和陀螺仪数据
        // 累加求平均
        HAL_Delay(5); // 5ms延时
    }
    
    // 计算偏移量
    accel_offset_x = accel_sum[0] / 200.0f / 16384.0f;
    accel_offset_y = accel_sum[1] / 200.0f / 16384.0f;
    accel_offset_z = (accel_sum[2] / 200.0f / 16384.0f) - 1.0f; // Z轴减去重力
    
    gyro_offset_x = gyro_sum[0] / 200;
    gyro_offset_y = gyro_sum[1] / 200;
    gyro_offset_z = gyro_sum[2] / 200;
}
```

### 2. 数据校准应用

#### 加速度计校准
```c
// 应用校准偏移
float ax = (accel[0] / 16384.0f) - accel_offset_x;
float ay = (accel[1] / 16384.0f) - accel_offset_y;
float az = (accel[2] / 16384.0f) - accel_offset_z;
```

#### 陀螺仪校准
```c
// 应用陀螺仪校准
float gz = (gyro[2] - gyro_offset_z) / 16.4f;
```

### 3. 数字滤波系统

#### 低通滤波器
```c
// 简单的低通滤波 (α = 0.8)
static float pitch_filtered = 0.0f;
static float roll_filtered = 0.0f;

pitch_filtered = 0.8f * pitch_filtered + 0.2f * pitch_raw;
roll_filtered = 0.8f * roll_filtered + 0.2f * roll_raw;
```

#### 滤波特性
- **截止频率**: 约2Hz
- **噪声抑制**: 80%的历史数据权重
- **响应速度**: 平衡稳定性和响应性

## 📊 优化效果

### 预期改善
1. **零点精度**: 静止时P≈0°, R≈0°
2. **数据稳定**: 减少±1°内的小幅波动
3. **噪声抑制**: 滤除高频振动干扰
4. **长期稳定**: 减少传感器漂移影响

### 校准流程
1. **系统启动**: 自动执行200次采样校准
2. **校准时间**: 约1秒钟(200×5ms)
3. **校准条件**: 要求传感器静止
4. **校准完成**: 设置calibration_done标志

## 🎯 使用说明

### 校准要求
1. **静止放置**: 校准期间保持MPU6050完全静止
2. **水平安装**: 确保Z轴垂直向上
3. **稳定环境**: 避免振动和冲击
4. **温度稳定**: 等待传感器温度稳定

### 校准时机
- **系统启动**: 每次开机自动校准
- **手动校准**: 可调用mpu6050_calibrate()
- **定期校准**: 建议长时间运行后重新校准

## 🔄 数据流程

### 校准后的数据处理流程
```
原始数据 → 校准偏移 → 物理单位转换 → 姿态解算 → 数字滤波 → 输出数据
```

### 具体步骤
1. **读取原始数据**: MPU_Get_Accelerometer/Gyroscope
2. **应用校准**: 减去偏移量
3. **单位转换**: 转换为g和dps
4. **姿态计算**: atan2计算角度
5. **数字滤波**: 低通滤波平滑
6. **输出结果**: 稳定的姿态角

## ⚠️ 注意事项

### 校准注意事项
1. **静止要求**: 校准期间绝对不能移动
2. **安装方向**: 确保传感器正确安装
3. **环境稳定**: 避免温度剧烈变化
4. **校准验证**: 校准后检查静止时的数据

### 滤波参数调整
```c
// 可调整的滤波系数
// α = 0.9: 更稳定，响应慢
// α = 0.5: 平衡稳定性和响应性
// α = 0.2: 响应快，可能不稳定
pitch_filtered = α * pitch_filtered + (1-α) * pitch_raw;
```

### 性能影响
- **校准时间**: 增加1秒启动时间
- **计算负载**: 增加约5%的CPU使用
- **内存使用**: 增加约40字节静态变量

## 🎮 验证方法

### 1. 静止测试
```
期望结果: P≈0°, R≈0°, Y稳定
L:0.0 R:0.0 | P:0.1 R:0.2 Y:3.2
L:0.0 R:0.0 | P:-0.1 R:0.1 Y:3.2
```

### 2. 倾斜测试
- **前后倾斜**: P角度应正确反映倾斜方向
- **左右倾斜**: R角度应正确反映倾斜方向
- **数据连续**: 角度变化应平滑连续

### 3. 转动测试
- **水平转动**: Y角度应连续变化
- **无跳变**: 避免±180°跳变
- **积分准确**: 转动360°后回到原点

## 🔄 故障排除

### 校准失败
- **检查连接**: 确认I2C连接正常
- **检查静止**: 校准期间保持完全静止
- **重新校准**: 调用mpu6050_calibrate()

### 数据异常
- **检查安装**: 确认传感器安装方向
- **调整滤波**: 修改滤波系数
- **环境检查**: 排除振动和磁场干扰

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**优化状态**: ✅ 校准+滤波完成  
**技术支持**: STM32循迹小车开发团队
