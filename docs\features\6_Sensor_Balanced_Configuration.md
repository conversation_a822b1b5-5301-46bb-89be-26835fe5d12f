# ⚖️ 6路传感器平衡配置方案

## 📊 配置概述

为了实现更好的平衡性，修改为使用中间6路传感器进行循迹：
- **忽略传感器**: P_1 (左端) 和 P_8 (右端)
- **有效传感器**: P_2, P_3, P_4, P_5, P_6, P_7
- **平衡特点**: 左右对称，中心平衡

## 🎯 平衡设计原理

### **传感器布局**
```
[X] [1] [2] [3] [4] [5] [6] [X]
P_1 P_2 P_3 P_4 P_5 P_6 P_7 P_8
忽略              有效传感器              忽略
```

### **权重分配**
```
传感器:  P_2  P_3  P_4  P_5  P_6  P_7
权重:    1000 2000 3000 4000 5000 6000
位置:    左侧      中心      右侧
```

### **中心计算**
```c
// 6路传感器的中心位置
center = (1000 + 6000) / 2 = 3500

// PID目标位置
pid_line.target = 3500.0f;
```

## ⚙️ 代码实现

### **Gray.c修改**
```c
void Gray_get(uint8_t *gray_buf)
{
    // 6路灰度传感器读取 (平衡配置，忽略两端)
    gray_buf[0] = 1;  // P_1忽略，设为1 (白线状态)
    gray_buf[1] = HAL_GPIO_ReadPin(P_2_GPIO_Port, P_2_Pin);   // 传感器2
    gray_buf[2] = HAL_GPIO_ReadPin(P_3_GPIO_Port, P_3_Pin);   // 传感器3
    gray_buf[3] = HAL_GPIO_ReadPin(P_4_GPIO_Port, P_4_Pin);   // 传感器4
    gray_buf[4] = HAL_GPIO_ReadPin(P_5_GPIO_Port, P_5_Pin);   // 传感器5
    gray_buf[5] = HAL_GPIO_ReadPin(P_6_GPIO_Port, P_6_Pin);   // 传感器6
    gray_buf[6] = HAL_GPIO_ReadPin(P_7_GPIO_Port, P_7_Pin);   // 传感器7
    gray_buf[7] = 1;  // P_8忽略，设为1 (白线状态)
}
```

### **循迹算法修改**
```c
// 计算线位置 (加权平均，使用中间6路传感器)
for(int i = 1; i <= 6; i++) {  // 使用P_2到P_7 (索引1-6)
    if(gray_buff[i] == 0) {  // 0表示检测到黑线
        line_position += i * 1000;  // 位置权重
        sensor_sum += 1000;
    }
}
```

### **PID目标调整**
```c
// 循迹PID参数：目标位置3500 (6路传感器中心)
PID_Init(&pid_line, 0.02f, 0.001f, 0.005f, 30.0f, -30.0f);
pid_line.target = 3500.0f;  // 6路传感器阵列中心
```

## 📊 串口输出格式

### **新的显示格式**
```
[TRACK] X110011X Pos:3500 PID:0.0 L:35 R:35
[TRACK] X011111X Pos:2000 PID:-30.0 L:65 R:5
[TRACK] X111100X Pos:5500 PID:30.0 L:5 R:65
```

### **格式说明**
- **X110011X**: 6路传感器状态，两端X表示忽略
- **Pos:3500**: 线位置 (1000-6000范围)
- **PID:0.0**: PID输出 (目标3500时为0)

## 🎮 平衡控制效果

### **位置检测范围**
```c
// 6路传感器的位置范围
最左: P_2检测 → Pos:1000
左偏: P_3检测 → Pos:2000  
左中: P_4检测 → Pos:3000
右中: P_5检测 → Pos:4000
右偏: P_6检测 → Pos:5000
最右: P_7检测 → Pos:6000
中心: P_4+P_5 → Pos:3500
```

### **PID控制响应**
```c
// 完美居中 (P_4和P_5都检测到)
Pos:3500 PID:0.0 L:35 R:35 → 直线前进

// 轻微左偏 (主要是P_4检测到)
Pos:3000 PID:-10.0 L:45 R:25 → 轻微右转

// 轻微右偏 (主要是P_5检测到)  
Pos:4000 PID:10.0 L:25 R:45 → 轻微左转

// 明显左偏 (P_3检测到)
Pos:2000 PID:-30.0 L:65 R:5 → 强力右转

// 明显右偏 (P_6检测到)
Pos:5000 PID:30.0 L:5 R:65 → 强力左转
```

## 📈 平衡优势

### **相比7路配置的优势**
- ✅ **左右对称**: 忽略两端，完全对称
- ✅ **中心平衡**: 目标3500，真正的中心
- ✅ **控制精度**: 减少边缘传感器的干扰
- ✅ **稳定性**: 更稳定的中心检测

### **相比8路配置的优势**
- ✅ **避免故障**: 忽略有问题的P_1
- ✅ **减少噪声**: 忽略可能不稳定的边缘传感器
- ✅ **平衡设计**: 左右完全对称
- ✅ **简化逻辑**: 更清晰的控制逻辑

## 🔧 参数优化

### **PID参数保持**
```c
// 当前参数适合6路配置
Kp = 0.02   // 比例系数
Ki = 0.001  // 积分系数  
Kd = 0.005  // 微分系数
输出范围 = ±30
目标位置 = 3500
```

### **如需调整**
```c
// 如果响应不够
PID_Init(&pid_line, 0.025f, 0.001f, 0.005f, 30.0f, -30.0f);

// 如果有振荡
PID_Init(&pid_line, 0.015f, 0.001f, 0.008f, 30.0f, -30.0f);
```

## 🎯 使用效果

### **循迹表现**
- **中心检测**: 更准确的中心位置检测
- **平衡转向**: 左右转向完全对称
- **稳定控制**: 减少边缘传感器干扰
- **精确定位**: 6路传感器提供足够精度

### **适用场景**
- **标准赛道**: 线宽适中的标准黑线
- **平衡要求**: 需要精确中心控制的场合
- **稳定优先**: 优先考虑稳定性的应用
- **对称设计**: 需要左右完全对称的控制

## 📊 性能对比

### **检测精度**
```
8路: 0-7000 (8个位置)
7路: 1000-7000 (7个位置，不平衡)
6路: 1000-6000 (6个位置，平衡) ✅
```

### **中心精度**
```
8路: 3500 (理论中心)
7路: 4000 (偏右的中心)
6路: 3500 (真正的中心) ✅
```

### **对称性**
```
8路: 完全对称 (但P_1有问题)
7路: 不对称 (左侧少一个)
6路: 完全对称 (忽略两端) ✅
```

## ⚠️ 注意事项

### **检测范围**
- **有效范围**: 1000-6000
- **中心位置**: 3500
- **边缘盲区**: 最左和最右端无检测

### **赛道要求**
- **线宽适中**: 不要太宽，避免超出6路范围
- **居中放置**: 确保传感器阵列居中对准
- **稳定光照**: 6路传感器需要一致的光照条件

## 🔍 故障排除

### **如果偏向一侧**
```c
// 检查传感器安装是否居中
// 检查6个传感器是否都正常工作
// 可以通过串口输出验证各传感器状态
```

### **如果检测不到线**
```c
// 可能线太细，只在两端
// 调整传感器高度
// 确认线在6路传感器覆盖范围内
```

---

**配置完成时间**: 2025-06-20  
**配置类型**: 6路平衡传感器配置  
**特点**: 左右对称 + 中心平衡 + 稳定控制  
**状态**: ✅ 已实现
