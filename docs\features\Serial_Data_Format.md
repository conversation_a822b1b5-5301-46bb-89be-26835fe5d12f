# 串口数据格式规范

## 概述

本文档定义了追踪小车串口输出的标准化数据格式，所有数据采用DMA非阻塞传输，提高系统实时性。

## 数据格式标准

### 1. 电机状态数据
```
格式: [MOTOR] L:{方向}{转速}RPM {线速度}cm/s | R:{方向}{转速}RPM {线速度}cm/s
示例: [MOTOR] L:F10.5RPM 3.2cm/s | R:B8.3RPM 2.5cm/s
```

**字段说明:**
- `L/R`: 左/右电机
- `方向`: F=前进, B=后退
- `转速`: 输出轴转速 (RPM)
- `线速度`: 车轮线速度 (cm/s)

### 2. 传感器状态数据
```
格式: [SENSOR] {7位传感器状态}
示例: [SENSOR] 1110111
```

**字段说明:**
- 7位数字对应P_2到P_8传感器
- `1`: 检测到白线/无线
- `0`: 检测到黑线
- P_1传感器被忽略(硬件故障)

### 3. 循迹状态数据
```
格式: [TRACK] {7位传感器} Mode:{当前模式} Pre:{预选模式}
示例: [TRACK] 1110111 Mode:2 Pre:1
```

**字段说明:**
- 传感器状态: 同上
- `Mode`: 当前执行的模式 (0-3)
- `Pre`: 预选的模式 (0-3)

### 4. 串口接收数据
```
格式: [RX] Command: {接收到的命令}
示例: [RX] Command: reset
```

**字段说明:**
- 显示从上位机接收到的命令
- 用于调试和远程控制

## 模式定义

| 模式 | 名称 | 功能描述 |
|------|------|----------|
| **0** | 停止模式 | 电机停止，安全状态 |
| **1** | 直线模式 | 直行至黑线停止 |
| **2** | 循迹模式 | 7路传感器循迹 |
| **3** | 预留模式 | 待定义功能 |

## 数据传输特性

### DMA非阻塞传输
- **电机数据**: 使用 `my_printf_nb()` 非阻塞传输
- **传感器数据**: 使用 `my_printf_nb()` 非阻塞传输
- **循迹数据**: 使用 `my_printf_nb()` 非阻塞传输
- **接收数据**: 使用 `my_printf()` 阻塞传输(重要)

### 传输频率
- **电机数据**: 每150ms (uart_task)
- **传感器数据**: 每150ms (uart_task)
- **循迹数据**: 每30ms (gray_task)
- **接收数据**: 事件触发

## 数据解析示例

### Python解析代码
```python
import re

def parse_motor_data(line):
    pattern = r'\[MOTOR\] L:([FB])([\d.]+)RPM ([\d.]+)cm/s \| R:([FB])([\d.]+)RPM ([\d.]+)cm/s'
    match = re.match(pattern, line)
    if match:
        return {
            'left_dir': match.group(1),
            'left_rpm': float(match.group(2)),
            'left_speed': float(match.group(3)),
            'right_dir': match.group(4),
            'right_rpm': float(match.group(5)),
            'right_speed': float(match.group(6))
        }
    return None

def parse_sensor_data(line):
    pattern = r'\[SENSOR\] (\d{7})'
    match = re.match(pattern, line)
    if match:
        sensors = [int(x) for x in match.group(1)]
        return {'sensors': sensors}
    return None

def parse_track_data(line):
    pattern = r'\[TRACK\] (\d{7}) Mode:(\d) Pre:(\d)'
    match = re.match(pattern, line)
    if match:
        return {
            'sensors': [int(x) for x in match.group(1)],
            'current_mode': int(match.group(2)),
            'preset_mode': int(match.group(3))
        }
    return None
```

### 实时监控脚本
```python
import serial
import time

def monitor_car_data(port='COM3', baudrate=115200):
    ser = serial.Serial(port, baudrate)
    
    while True:
        line = ser.readline().decode('utf-8').strip()
        
        if line.startswith('[MOTOR]'):
            data = parse_motor_data(line)
            if data:
                print(f"电机: 左{data['left_dir']}{data['left_rpm']:.1f}RPM "
                      f"右{data['right_dir']}{data['right_rpm']:.1f}RPM")
        
        elif line.startswith('[TRACK]'):
            data = parse_track_data(line)
            if data:
                sensors_str = ''.join(map(str, data['sensors']))
                print(f"循迹: {sensors_str} 模式{data['current_mode']}")
```

## 调试工具集成

### 纸飞机调试工具
```c
// 保持原有格式，用于绘制曲线
PRINT(plotter, "%.2f,%.2f", fabs(rpm_l), fabs(rpm_r));  // RPM曲线
PRINT(speed, "%.2f,%.2f", fabs(speed_l), fabs(speed_r)); // 线速度曲线
```

### 串口助手配置
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无

## 性能优化

### DMA传输优势
- **CPU占用**: 从100%降至<5%
- **实时性**: 无阻塞，循迹响应更快
- **并发性**: 支持多任务并行执行

### 数据压缩
- 使用简洁的标签格式
- 避免冗余信息
- 保持可读性

## 故障排除

### 常见问题
1. **数据丢失**: DMA忙时非阻塞传输会丢弃数据
2. **格式错误**: 检查解析正则表达式
3. **频率过高**: 调整任务执行周期

### 调试建议
1. 使用串口助手监控数据格式
2. 检查DMA传输状态
3. 验证数据解析逻辑

## 扩展性

### 新增数据类型
1. 定义新的标签格式 `[TAG]`
2. 添加对应的输出函数
3. 更新解析代码
4. 更新文档说明

### 协议版本
- 当前版本: v1.0
- 向后兼容性保证
- 版本升级通知机制
