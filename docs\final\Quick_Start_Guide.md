# 🚀 STM32F407循迹小车快速入门指南

## 📋 概述

本指南将帮助您快速上手STM32F407循迹小车系统，从硬件连接到软件运行，让您在最短时间内体验完整的循迹功能。

## 🎯 系统特性

### 核心功能
- **🎯 智能循迹**: 7路灰度传感器精确跟踪
- **🔄 多种模式**: 4种运行模式可选
- **⚡ 实时监控**: 串口数据和纸飞机工具
- **🏗️ 模块化设计**: 基于motor_driver架构

### 技术规格
- **主控**: STM32F407VET6 (168MHz)
- **电机**: MG513XP28双电机 (28:1减速比)
- **传感器**: 7路灰度传感器阵列
- **通信**: UART1 DMA (115200波特率)

## 📋 系统检查清单

### **硬件检查**
- [ ] **电源连接**: 确认电池电量充足
- [ ] **电机连接**: 左右电机连接正确
- [ ] **编码器连接**: E1A/E1B (左) 和 E2A/E2B (右) 连接正常
- [ ] **传感器连接**: P_2到P_8七路传感器连接正确
- [ ] **声光设备**: PF11连接到声光控制设备

### **软件检查**
- [ ] **编译成功**: 无编译错误和警告
- [ ] **下载成功**: 程序正确下载到MCU
- [ ] **串口连接**: 115200波特率串口连接正常
- [ ] **初始化完成**: 系统启动后无异常

## 🔧 硬件连接

### 必需组件
- STM32F407VET6开发板
- MG513XP28双电机 (带编码器)
- 7路灰度传感器模块
- 3个按键模块
- 连接线材
- 12V电源

### 硬件连接图

#### 电机连接
```
左电机 (Motor B):
- PWM: PA7 (TIM3_CH2)
- 方向控制: PC5 (Bin1), PC4 (Bin2)
- 编码器: PE9 (E2A), PE11 (E2B)

右电机 (Motor A):
- PWM: PA6 (TIM3_CH1)
- 方向控制: PA4 (Ain1), PA5 (Ain2)
- 编码器: PA0 (E1A), PA1 (E1B)
```

#### 传感器连接
```
灰度传感器:
- P_1: PF0 (硬件故障，已忽略)
- P_2: PE5
- P_3: PE6
- P_4: PF1
- P_5: PF3
- P_6: PF5
- P_7: PF7
- P_8: PF9
```

#### 控制接口
```
按键:
- KEY0: PE2 (模式预选)
- KEY1: PE3 (模式预选)
- KEY2: PE4 (确认执行)

串口:
- TX: PA9
- RX: PA10
- 波特率: 115200

LED/蜂鸣器:
- PF11 (低电平触发)
```

## 🎯 快速启动步骤

### **第1步: 硬件准备**
1. **放置小车**: 将小车放在白色表面上
2. **传感器检查**: 确认传感器距离地面2-5mm
3. **赛道准备**: 准备白底黑线的测试赛道
4. **串口连接**: 连接串口调试工具

### **第2步: 系统启动**
1. **上电**: 给小车上电，系统自动初始化
2. **观察串口**: 应该看到以下输出
```
[SYSTEM] Car Motor Driver Init Success
L:F0.0RPM 0.0cm/s | R:F0.0RPM 0.0cm/s
{plotter}0.00,0.00
P2:1 P3:1 P4:1 P5:1 P6:1 P7:1 P8:1 | L:0F R:0F | MODE0 STOP
```

### **第3步: 传感器测试**
1. **手动测试**: 用黑色物体遮挡传感器
2. **观察变化**: 串口应显示传感器状态变化
```
P2:1 P3:1 P4:1 P5:1 P6:1 P7:1 P8:1 | L:0F R:0F | MODE0 STOP  // 全白
P2:1 P3:0 P4:0 P5:1 P6:1 P7:1 P8:1 | L:0F R:0F | MODE0 STOP  // 检测到黑线
P2:1 P3:1 P4:1 P5:1 P6:1 P7:1 P8:1 | L:0F R:0F | MODE0 STOP  // 移开黑色物体
```

### **第4步: 模式切换测试**
1. **按键操作**:
   - 按KEY0或KEY1预选模式
   - 按KEY2确认执行
2. **观察变化**: 串口显示模式切换
```
P2:1 P3:1 P4:1 P5:1 P6:1 P7:1 P8:1 | L:0F R:0F | MODE1 STRAIGHT
P2:1 P3:1 P4:1 P5:1 P6:1 P7:1 P8:1 | L:0F R:0F | MODE2 TRACKING
```

### **第5步: 循迹测试**
1. **选择模式2**: 按键切换到循迹模式
2. **放置小车**: 将小车放在黑线上
3. **观察循迹**: 小车应该自动开始循迹
```
P2:1 P3:0 P4:0 P5:1 P6:1 P7:1 P8:1 | L:35F R:35F | MODE2 TRACKING [BEEP]
```
3. **监控数据**: 观察PID控制效果
```
[TRACK] X110011X Pos:3500 PID:0.0 L:35 R:35    // 直行
[TRACK] X100111X Pos:2500 PID:-20.0 L:55 R:15  // 左转
[TRACK] X111001X Pos:5500 PID:20.0 L:15 R:55   // 右转
```

## 📊 正常运行状态

### **串口输出示例**
```
L:F50.2RPM 16.4cm/s | R:F49.8RPM 16.3cm/s     // 电机状态
{plotter}50.20,49.80                           // 纸飞机数据
[TRACK] X110011X Pos:3500 PID:0.0 L:35 R:35   // 循迹状态
```

### **数据含义**
- **L:F50.2RPM**: 左电机前进50.2RPM
- **16.4cm/s**: 左轮线速度16.4cm/s
- **{plotter}**: 纸飞机工具数据
- **X110011X**: 6路传感器状态 (X表示忽略的P_1和P_8)
- **Pos:3500**: 线位置3500 (中心位置)
- **PID:0.0**: PID输出0.0 (完美居中)
- **L:35 R:35**: 左右轮速度35%

## 🔧 常见问题解决

### **问题1: 小车不动**
**现象**: 串口有输出，但小车不移动
**检查**:
- [ ] 电机连接是否正确
- [ ] 电源电压是否足够
- [ ] PWM输出是否正常
- [ ] 电机驱动是否正常

**解决**: 检查car.c中的电机控制逻辑

### **问题2: 循迹不稳定**
**现象**: 小车在黑线上摆动或偏离
**检查**:
- [ ] 传感器高度是否合适
- [ ] 光照是否均匀
- [ ] PID参数是否合适
- [ ] 线宽是否合适

**解决**: 调整PID参数或传感器位置

### **问题3: 声光不触发**
**现象**: 检测到黑线但没有声光提示
**检查**:
- [ ] PF11连接是否正确
- [ ] 传感器是否正常工作
- [ ] 状态变化逻辑是否正确

**解决**: 检查gray_task中的声光控制代码

### **问题4: 串口无输出**
**现象**: 串口工具连接但无数据
**检查**:
- [ ] 波特率是否为115200
- [ ] 串口线连接是否正确
- [ ] UART初始化是否正常

**解决**: 检查串口配置和连接

## ⚙️ 参数快速调整

### **速度调整**
```c
// 在scheduler.c的gray_task中
int base_speed = 35;  // 改为20-50调整速度
```

### **循迹精度调整**
```c
// 在gray_task中的PID初始化
PID_Init(&pid_line, 0.02f, 0.001f, 0.005f, 30.0f, -30.0f);
//                  ↑Kp   ↑Ki     ↑Kd     ↑最大输出
// Kp增大 = 响应更快，但可能振荡
// Kd增大 = 更稳定，但响应变慢
```

### **电机速度调整**
```c
// 在pid.c的pid_speed_task中
pid_left.target = 50.0f;   // 改为30-70调整目标RPM
pid_right.target = 50.0f;
```

## 📈 性能优化建议

### **提高循迹精度**
1. **降低速度**: base_speed = 25
2. **增加阻尼**: Kd = 0.01
3. **减小输出**: max_output = 25

### **提高循迹速度**
1. **增加速度**: base_speed = 45
2. **增强响应**: Kp = 0.025
3. **增大输出**: max_output = 35

### **提高稳定性**
1. **平衡参数**: Kp=0.02, Ki=0.001, Kd=0.008
2. **适中速度**: base_speed = 35
3. **合理输出**: max_output = 30

## 🎯 测试建议

### **基础测试**
1. **直线测试**: 在直线上测试跟线效果
2. **弯道测试**: 在弯道上测试转向性能
3. **速度测试**: 不同速度下的稳定性
4. **声光测试**: 多次触发测试可靠性

### **高级测试**
1. **复杂路径**: 测试S型弯道
2. **连续运行**: 长时间运行稳定性
3. **环境适应**: 不同光照条件下的表现
4. **参数优化**: 针对特定赛道优化参数

## 📞 技术支持

### **调试工具**
- **串口监控**: 实时观察系统状态
- **纸飞机工具**: 监控电机速度曲线
- **示波器**: 观察PWM和编码器信号
- **万用表**: 检查电源和信号电压

### **文档参考**
- `Complete_Tracking_Car_System.md` - 完整系统说明
- `PID_Line_Following_Solution.md` - PID循迹详解
- `6_Sensor_Balanced_Configuration.md` - 6路传感器配置
- `Sound_Light_Control_Implementation.md` - 声光控制说明

---

**快速启动指南版本**: v1.0  
**适用系统**: 6路PID循迹小车  
**更新时间**: 2025-06-20  
**状态**: ✅ 可直接使用
