/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define key0_Pin GPIO_PIN_2
#define key0_GPIO_Port GPIOE
#define key1_Pin GPIO_PIN_3
#define key1_GPIO_Port GPIOE
#define key2_Pin GPIO_PIN_4
#define key2_GPIO_Port GPIOE
#define P_2_Pin GPIO_PIN_5
#define P_2_GPIO_Port GPIOE
#define P_3_Pin GPIO_PIN_6
#define P_3_GPIO_Port GPIOE
#define P_1_Pin GPIO_PIN_0
#define P_1_GPIO_Port GPIOF
#define P_4_Pin GPIO_PIN_1
#define P_4_GPIO_Port GPIOF
#define P_5_Pin GPIO_PIN_3
#define P_5_GPIO_Port GPIOF
#define P_6_Pin GPIO_PIN_5
#define P_6_GPIO_Port GPIOF
#define P_7_Pin GPIO_PIN_7
#define P_7_GPIO_Port GPIOF
#define P_8_Pin GPIO_PIN_9
#define P_8_GPIO_Port GPIOF
#define E1A_Pin GPIO_PIN_0
#define E1A_GPIO_Port GPIOA
#define E1B_Pin GPIO_PIN_1
#define E1B_GPIO_Port GPIOA
#define Ain1_Pin GPIO_PIN_4
#define Ain1_GPIO_Port GPIOA
#define Ain2_Pin GPIO_PIN_5
#define Ain2_GPIO_Port GPIOA
#define PWMA_Pin GPIO_PIN_6
#define PWMA_GPIO_Port GPIOA
#define PWMB_Pin GPIO_PIN_7
#define PWMB_GPIO_Port GPIOA
#define Bin2_Pin GPIO_PIN_4
#define Bin2_GPIO_Port GPIOC
#define Bin1_Pin GPIO_PIN_5
#define Bin1_GPIO_Port GPIOC
#define FMD_LED_Pin GPIO_PIN_11
#define FMD_LED_GPIO_Port GPIOF
#define E2A_Pin GPIO_PIN_9
#define E2A_GPIO_Port GPIOE
#define E2B_Pin GPIO_PIN_11
#define E2B_GPIO_Port GPIOE
#define SCL_Pin GPIO_PIN_14
#define SCL_GPIO_Port GPIOE
#define SDA_Pin GPIO_PIN_15
#define SDA_GPIO_Port GPIOE

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
