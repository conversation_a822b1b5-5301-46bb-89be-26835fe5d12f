[Version]
Nu_LinkVersion=V6.14
[Process]
ProcessID=0x00000000
ProcessCreationTime_L=0x00000000
ProcessCreationTime_H=0x00000000
NuLinkID=0x00000000
DisableFirmwareUpdate=0
[ChipSelect]
;ChipName=<NUC1xx|NUC2xx|M05x|N571|N572|Nano100|N512|Mini51|NUC505|General>
ChipName=NUC1xx
[NUC505]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NUC505_SPIFLASH.FLM
[NUC4xx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NUC400_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x014fb180
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[NUC2xx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC200_AP_128.FLM
[NUC1311]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC1311_AP_64.FLM
[NUC1263]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=NUC1263_AP_64.FLM
[NUC126]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=NUC126_AP_256.FLM
[NUC121]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC121_AP_32.FLM
[NUC1xx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC100_AP_128.FLM
[NUC029]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NUC029_AP_16.FLM
[NM1820]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1820_AP_17_5.FLM
[NM1810]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1810_AP_29_5.FLM
[NM1500]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1500_AP_128.FLM
[NM1330]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1330_AP_64.FLM
[NM1320]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1320_AP_32.FLM
[NM1240]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1240_AP_64.FLM
[NM1230]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1230_AP_64.FLM
[NM1200]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1200_AP_8.FLM
[NM1120]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1120_AP_29_5.FLM
[N32F030]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N32F030_AP_64.FLM
[TF5100]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=TF5100_AP_64.FLM
[NDA102]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NDA102_AP_29_5.FLM
[Nano103]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=Nano103_AP_64.FLM
[Nano100]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=Nano100_AP_64.FLM
[NSC74]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NSC74_AP_512.FLM
[NSC128]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=NSC128_AP_320.FLM
[N576]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N576_AP_145.FLM
[N575]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N575_AP_145.FLM
[N574]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N574_AP_512.FLM
[N572]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=N572Fxxx.FLM
[N571]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=N571E000.FLM
[N570]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N570_AP_64.FLM
[N569]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N569_AP_64.FLM
[N512]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N512_AP_64.FLM
[Mini57]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=Mini57_AP_29_5.FLM
[Mini51]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=Mini51_AP_16.FLM
[M55M1/M5531]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
SPIM=0
SPIMOption=0xAD000000
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
CheckDPM=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M55M1_AP_2M.FLM
ProgramAlgorithm1=M55M1_SPIM.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M481]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M481_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M480LD]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M480LD_AP_256.FLM
[M479]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M479_AP_256.FLM
[M471]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M471_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M460]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
SPIM=0
SPIMOption=0xAD000000
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x8000
ProgramAlgorithm=M460_AP_1M.FLM
ProgramAlgorithm1=M460_SPIM_AP_1M.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M451]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M451_AP_256.FLM
[M433]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M433_AP_128.FLM
[M2L31]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x8000
ProgramAlgorithm=M2L31_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M2354]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
CheckDPM=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M2354_AP_1M.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M2351]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M2351_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M261]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M261_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M251]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M251_AP_192.FLM
[MR63]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=MR63_AP_512.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
[M2A23]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M2A23_AP_256.FLM
[M2003]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=M2003_AP_32.FLM
[M091]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
DisableTimeoutDetect=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M091_AP_64.FLM
[M071]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M071_AP_128.FLM
[M0564]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=M0564_AP_256.FLM
[M0519]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=M0519_AP_128.FLM
[M0518]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=M0518_AP_64.FLM
[M05x]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M0516_AP_64.FLM
[M0A86]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=M0A86_AP_64.FLM
[M0A21]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M0A21_AP_32.FLM
[M030G]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
DisableTimeoutDetect=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M030G_AP_64.FLM
[M031]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Bank=0
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M031_AP_128.FLM
[NPCX]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NPCX_AP_512.FLM
[KM1M7C]
Connect=0
Reset=Autodetect
MaxClock=4MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x00004000
ProgramAlgorithm=KM1M7C_I.FLM
ProgramAlgorithm1=KM1M7C_D0.FLM
ProgramAlgorithm2=KM1M7C_D1.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
EnableKeyfile=0
Keycode0=0xFFFFFFFF
Keycode1=0xFFFFFFFF
Keycode2=0xFFFFFFFF
Keycode3=0xFFFFFFFF
[KM1M7A/B]
Connect=0
Reset=Autodetect
MaxClock=4MHz
MemoryVerify=0
IOVoltage=5000
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x00004000
ProgramAlgorithm=KM1M7AFxxx_I.FLM
ProgramAlgorithm1=KM1M7AFxxx_D.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
EnableKeyfile=0
Keycode0=0xFFFFFFFF
Keycode1=0xFFFFFFFF
Keycode2=0xFFFFFFFF
Keycode3=0xFFFFFFFF
[KM1M4B]
Connect=0
Reset=Autodetect
MaxClock=4MHz
MemoryVerify=0
IOVoltage=5000
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x00004000
ProgramAlgorithm=KM1M4B_I.FLM
ProgramAlgorithm1=KM1M4B_D.FLM
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
EnableKeyfile=0
Keycode0=0xFFFFFFFF
Keycode1=0xFFFFFFFF
Keycode2=0xFFFFFFFF
Keycode3=0xFFFFFFFF
[KM1M0G]
Connect=2
Reset=HWRESET
MaxClock=4MHz
MemoryVerify=0
IOVoltage=5000
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x00004000
ProgramAlgorithm=KM1M0G_I.FLM
EnableKeyfile=0
Keycode0=0xFFFFFFFF
Keycode1=0xFFFFFFFF
Keycode2=0xFFFFFFFF
Keycode3=0xFFFFFFFF
[KM1M0D]
Connect=0
Reset=Autodetect
MaxClock=4MHz
MemoryVerify=0
IOVoltage=5000
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x00004000
ProgramAlgorithm=KM1M0D_I.FLM
ProgramAlgorithm1=KM1M0D_D.FLM
EnableKeyfile=0
Keycode0=0xFFFFFFFF
Keycode1=0xFFFFFFFF
Keycode2=0xFFFFFFFF
Keycode3=0xFFFFFFFF
[I96000]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=2
Program=0
Verify=0
ResetAndRun=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x8000
ProgramAlgorithm=
[I94000]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=I94000_AP_512.FLM
[I91500]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=I91500_AP_64.FLM
[ISD9300]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9300_AP_145.FLM
[I9200]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=I9200_AP_128.FLM
[ISD9xxx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9100_AP_145.FLM
[ISD9000]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9000_AP_64.FLM
[CM2003]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=1
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=CM2003_AP_32.FLM
[AU9xxx]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=AU9100_AP_145.FLM
[Autodetect]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=
[General]
Connect=0
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=
TraceConf0=0x00000002
TraceConf1=0x00b71b00
TraceConf2=0x00000800
TraceConf3=0x00000000
TraceConf4=0x00000001
TraceConf5=0x00000000
