# 🚗 简单循迹控制系统

## 📊 系统概述

移除了复杂的PID控制，采用简单直观的阈值控制方式实现循迹功能：
- **7路传感器**: 使用P_2到P_8进行线位置检测
- **阈值控制**: 基于位置阈值的简单转向控制
- **三种状态**: 左转、右转、直行三种基本控制状态

## 🎯 控制算法

### **位置检测算法**
```c
// 计算线位置 (加权平均，使用7路传感器)
int line_position = 0;
int sensor_sum = 0;

for(int i = 1; i < 8; i++) {  // 使用P_2到P_8 (索引1-7)
    if(gray_buff[i] == 0) {  // 0表示检测到黑线
        line_position += i * 1000;  // 位置权重
        sensor_sum += 1000;
    }
}

if(sensor_sum > 0) {
    line_position = line_position / sensor_sum;  // 计算加权平均位置
}
```

### **阈值控制逻辑**
```c
// 简单的阈值控制
if(line_position < 3000) {
    // 线在左侧，右转
    move_custom(20, 50);
    my_printf(&huart1," RIGHT");
} else if(line_position > 5000) {
    // 线在右侧，左转
    move_custom(50, 20);
    my_printf(&huart1," LEFT");
} else {
    // 线在中间，直行
    move_forward(35);
    my_printf(&huart1," STRAIGHT");
}
```

## 🔧 控制参数

### **阈值设置**
```
位置范围: 1000 - 7000
左转阈值: > 5000 (线在右侧)
右转阈值: < 3000 (线在左侧)
直行范围: 3000 - 5000 (线在中间)
```

### **速度设置**
```c
// 转向速度
左转: move_custom(50, 20)  // 左轮快，右轮慢
右转: move_custom(20, 50)  // 左轮慢，右轮快
直行: move_forward(35)     // 双轮同速

// 速度说明
50: 较快速度，用于转向时的外侧轮
35: 中等速度，用于直行
20: 较慢速度，用于转向时的内侧轮
```

## 📊 控制状态

### **三种控制状态**
```
状态1: LEFT (左转)
- 触发条件: line_position > 5000
- 控制动作: move_custom(50, 20)
- 物理含义: 线在右侧，需要左转修正

状态2: RIGHT (右转)  
- 触发条件: line_position < 3000
- 控制动作: move_custom(20, 50)
- 物理含义: 线在左侧，需要右转修正

状态3: STRAIGHT (直行)
- 触发条件: 3000 ≤ line_position ≤ 5000
- 控制动作: move_forward(35)
- 物理含义: 线在中间，保持直行
```

### **丢线处理**
```c
// 没有检测到线的情况
if(sensor_sum == 0) {
    my_printf(&huart1," NO_LINE");
    move_forward(35);  // 继续直行
}
```

## 📈 串口监控输出

### **输出格式**
```
[TRACK] X1100111 Pos:4000 STRAIGHT
[TRACK] X0111111 Pos:2000 RIGHT
[TRACK] X1111100 Pos:6000 LEFT
[TRACK] X1111111 NO_LINE
```

### **信息解读**
- **X1100111**: 7路传感器状态 (X=忽略的P_1)
- **Pos:4000**: 线位置值 (1000-7000)
- **STRAIGHT/LEFT/RIGHT**: 当前控制状态
- **NO_LINE**: 未检测到线

## 🎮 控制效果

### **位置与控制映射**
```
位置1000-2999: RIGHT (右转)
├─ 1000: 最左 → 右转
├─ 2000: 左偏 → 右转  
└─ 2999: 左中 → 右转

位置3000-5000: STRAIGHT (直行)
├─ 3000: 左边界 → 直行
├─ 4000: 中心 → 直行
└─ 5000: 右边界 → 直行

位置5001-7000: LEFT (左转)
├─ 5001: 右中 → 左转
├─ 6000: 右偏 → 左转
└─ 7000: 最右 → 左转
```

### **转向特性**
- **离散控制**: 只有3种控制状态
- **固定速度**: 每种状态的速度固定
- **快速响应**: 无复杂计算，响应迅速
- **简单可靠**: 逻辑简单，不易出错

## 🔧 参数调整

### **阈值调整**
```c
// 更敏感的控制 (更早转向)
if(line_position < 3500) turn_right();
if(line_position > 4500) turn_left();

// 更保守的控制 (更晚转向)  
if(line_position < 2500) turn_right();
if(line_position > 5500) turn_left();
```

### **速度调整**
```c
// 更快的循迹
move_custom(60, 30);  // 左转
move_custom(30, 60);  // 右转
move_forward(45);     // 直行

// 更慢的循迹
move_custom(40, 15);  // 左转
move_custom(15, 40);  // 右转  
move_forward(25);     // 直行
```

### **转向强度调整**
```c
// 更强的转向 (速度差更大)
move_custom(60, 10);  // 左转，速度差50
move_custom(10, 60);  // 右转，速度差50

// 更温和的转向 (速度差更小)
move_custom(45, 25);  // 左转，速度差20
move_custom(25, 45);  // 右转，速度差20
```

## 📊 性能特点

### **优势**
- ✅ **简单可靠**: 逻辑简单，不易出错
- ✅ **响应快速**: 无复杂计算，实时响应
- ✅ **易于调试**: 只有3种状态，便于观察
- ✅ **资源节省**: 无需复杂的PID计算
- ✅ **参数少**: 只需调整阈值和速度

### **局限性**
- ❌ **控制粗糙**: 只有3种离散状态
- ❌ **可能振荡**: 在阈值边界可能来回切换
- ❌ **适应性差**: 固定参数，难以适应不同情况
- ❌ **精度有限**: 无法实现精确的位置控制

## 🎯 适用场景

### **推荐使用**
- **简单赛道**: 直线和缓弯为主的赛道
- **学习阶段**: 理解循迹基本原理
- **调试阶段**: 验证传感器和基本功能
- **资源受限**: CPU或内存资源有限的场合

### **不推荐使用**
- **复杂赛道**: 急弯、S弯等复杂路径
- **高精度要求**: 需要精确控制的场合
- **高速循迹**: 高速下的稳定控制
- **竞赛应用**: 对性能要求较高的竞赛

## 🔍 故障排除

### **循迹不稳定**
```c
// 可能原因：阈值设置不当
// 解决：调整阈值范围
if(line_position < 2800) turn_right();  // 降低右转阈值
if(line_position > 5200) turn_left();   // 提高左转阈值
```

### **转向过度**
```c
// 可能原因：转向速度差太大
// 解决：减小速度差
move_custom(40, 30);  // 左转，减小速度差
move_custom(30, 40);  // 右转，减小速度差
```

### **响应迟钝**
```c
// 可能原因：阈值范围太小
// 解决：扩大阈值范围
if(line_position < 3500) turn_right();  // 提高右转阈值
if(line_position > 4500) turn_left();   // 降低左转阈值
```

## 📈 升级路径

### **如需更好性能**
1. **增加状态**: 添加"轻微左转"、"轻微右转"状态
2. **渐变控制**: 根据偏差程度调整转向强度
3. **自适应阈值**: 根据实际情况动态调整阈值
4. **PID控制**: 升级为连续的PID控制系统

### **代码扩展示例**
```c
// 5状态控制
if(line_position < 2500) {
    move_custom(15, 55);  // 强右转
} else if(line_position < 3500) {
    move_custom(25, 45);  // 轻右转
} else if(line_position > 5500) {
    move_custom(55, 15);  // 强左转
} else if(line_position > 4500) {
    move_custom(45, 25);  // 轻左转
} else {
    move_forward(35);     // 直行
}
```

---

**实现完成时间**: 2025-06-20  
**控制类型**: 简单阈值控制  
**特点**: 简单可靠 + 快速响应 + 易于调试  
**状态**: ✅ 已实现
