# 🚀 MG513XP28双电机速度显示快速测试指南

## 📋 测试准备

### 硬件检查
- ✅ 左电机编码器: PA0(A相), PA1(B相)
- ✅ 右电机编码器: PE9(A相), PE11(B相)
- ✅ 串口连接: PA9(TX) → 串口调试器RX
- ✅ 电机供电: 确保6-12V供电正常

### 软件设置
- ✅ 串口工具: 115200波特率, 8N1
- ✅ 编码器: TIM1和TIM2已启动
- ✅ 电机控制: 15%PWM前进模式
- ✅ 数据输出: 每150ms更新一次

## 🎯 预期测试结果

### 正常运行输出
```
L:F2.5RPM 0.8cm/s | R:F2.3RPM 0.7cm/s
L:F2.4RPM 0.8cm/s | R:F2.4RPM 0.8cm/s
L:F2.6RPM 0.8cm/s | R:F2.2RPM 0.7cm/s
```

### 数据说明
- **L/R**: 左/右电机
- **F**: 前进方向 (B为后退)
- **RPM**: 输出轴转速
- **cm/s**: 轮子线速度

## 🔍 测试步骤

### 1. 基础连接测试
1. 连接串口调试工具
2. 设置115200波特率
3. 上电启动系统
4. 观察是否有数据输出

### 2. 静止状态测试
- **预期**: `L:F0.0RPM 0.0cm/s | R:F0.0RPM 0.0cm/s`
- **说明**: 电机静止时速度应为0

### 3. 运行状态测试
- **预期**: 左右轮都显示正向转速
- **范围**: 1-5 RPM (15%PWM下)
- **一致性**: 左右轮速度差异<20%

### 4. 方向测试
- **手动反转**: 手动反向转动电机
- **预期**: 方向显示为'B'(后退)
- **恢复**: 松手后恢复'F'(前进)

## 📊 故障排除

### 问题1: 无串口输出
**现象**: 串口工具无任何数据
**检查**:
- 串口线连接是否正确
- 波特率是否为115200
- 系统是否正常启动(LED闪烁)

### 问题2: 速度始终为0
**现象**: 显示 `L:F0.0RPM | R:F0.0RPM`
**检查**:
- 编码器是否连接
- 电机是否有电
- 编码器供电是否正常

### 问题3: 只有一个电机有数据
**现象**: 只有L或R有速度显示
**检查**:
- 检查对应编码器连接
- 确认TIM1和TIM2配置
- 检查电机驱动电路

### 问题4: 方向显示错误
**现象**: 前进时显示'B'
**解决**: 交换编码器A/B相线

### 问题5: 速度值异常
**现象**: 速度过高或过低
**检查**:
- 编码器分辨率设置
- 减速比参数
- 轮径参数

## 🔧 参数调整

### 如果速度显示不准确
```c
// 在scheduler.c中调整参数
#define ENCODER_PPR 13          // MG513XP28实际分辨率
#define GEAR_RATIO 28           // 减速比
#define WHEEL_DIAMETER_CM 6.5   // 实际轮径
```

### 如果需要改变电机速度
```c
// 在car_task中修改PWM值
car_run("left", 20, 1);   // 改为20%PWM
car_run("right", 20, 1);
```

### 如果需要改变输出频率
```c
// 在scheduler_task中修改
{uart_task, 100, 0},  // 改为100ms输出一次
```

## 📈 性能验证

### 正常指标
- **输出频率**: 每150ms一次数据
- **速度范围**: 1-5 RPM (15%PWM)
- **方向准确**: F表示前进
- **左右一致**: 速度差异<20%

### 优化建议
- 如果速度波动大，可以增加滤波
- 如果需要更高精度，可以提高采样频率
- 如果需要更多信息，可以添加电机轴转速显示

## 🎉 成功标志

当您看到类似以下输出时，说明系统工作正常：
```
L:F2.5RPM 0.8cm/s | R:F2.3RPM 0.7cm/s
L:F2.4RPM 0.8cm/s | R:F2.4RPM 0.8cm/s
L:F2.6RPM 0.8cm/s | R:F2.2RPM 0.7cm/s
```

这表明：
- ✅ 双电机都在正常运行
- ✅ 编码器数据正确读取
- ✅ 速度计算准确
- ✅ 方向检测正确
- ✅ 串口通信正常

---

**测试完成后**: 您就可以看到两个MG513XP28电机的实时速度和方向信息了！
