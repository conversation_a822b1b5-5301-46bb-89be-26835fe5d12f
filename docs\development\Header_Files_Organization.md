# 📋 头文件和全局变量整理文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: Alex (工程师)
- **状态**: 头文件和全局变量整理完成
- **目的**: 统一管理全局变量声明，规范头文件包含

## 🎯 整理目标

### 核心原则
- ✅ **集中管理**: 所有全局变量声明集中在mydefine.h
- ✅ **避免重复**: 消除重复的extern声明
- ✅ **清晰分类**: 按功能模块分类组织全局变量
- ✅ **统一包含**: 所有模块统一包含mydefine.h

## 📁 mydefine.h 全局变量声明

### 完整的全局变量声明
```c
#ifndef MYDEFINE_H
#define MYDEFINE_H

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdlib.h>
#include <main.h>
#include <usart.h>
#include "tim.h"

// UART相关全局变量
extern uint32_t uart_rx_ticks;
extern uint16_t uart_rx_index;
extern uint8_t uart_rx_buffer[128];

// 系统模式相关全局变量
extern uint8_t sys_mode;	//0-STOP 1-(A-B) 2-(ABCD) 3-(ACBD)
extern uint8_t sys_ture;
extern uint8_t pre_mode;    // 预选模式

// 灰度传感器相关全局变量
extern uint8_t gray_buff[8];

// MPU6050相关全局变量
extern float Pitch, Roll, Yaw;

#endif
```

### 变量分类说明

#### **UART通信变量**
- `uart_rx_ticks`: UART接收时间戳
- `uart_rx_index`: UART接收缓冲区索引
- `uart_rx_buffer[128]`: UART接收缓冲区

#### **系统模式变量**
- `sys_mode`: 当前执行模式 (0=停止, 1=直线, 2=循迹, 3=预留)
- `sys_ture`: 系统启动标志 (0=未启动, 1=启动)
- `pre_mode`: 预选模式 (按键选择的模式)

#### **传感器数据变量**
- `gray_buff[8]`: 灰度传感器数据缓冲区
- `Pitch, Roll, Yaw`: MPU6050姿态角数据

## 🔧 变量定义位置

### 各模块变量定义
```c
// key.c - 按键和模式控制
uint8_t sys_mode;     // 当前执行模式
uint8_t sys_ture;     // 启动标志
uint8_t pre_mode;     // 预选模式

// scheduler.c - 调度器和传感器
uint8_t gray_buff[8]; // 灰度传感器缓冲区

// mpu6050_app.c - MPU6050数据
float Pitch, Roll, Yaw; // 姿态角数据

// my_uart.c - UART通信 (推测)
uint32_t uart_rx_ticks;
uint16_t uart_rx_index;
uint8_t uart_rx_buffer[128];
```

## 📊 头文件包含规范

### 标准包含顺序
```c
// 1. 自身头文件
#include "module_name.h"

// 2. 系统定义头文件
#include "mydefine.h"

// 3. 相关模块头文件
#include "Gray.h"
#include "mpu6050_app.h"

// 4. 标准库头文件
#include <stdio.h>
#include <string.h>
```

### 各模块头文件包含

#### **oled_app.c**
```c
#include "oled_app.h"
#include "oled/oled.h"
#include "mydefine.h"        // 包含所有全局变量声明
#include "Gray.h"
#include "mpu6050_app.h"
#include <stdio.h>
#include <stdarg.h>

// 全局变量已在mydefine.h中声明
```

#### **scheduler.c**
```c
#include <stdint.h>
#include "mydefine.h"        // 包含所有全局变量声明
#include "scheduler.h"
#include "key.h"
#include "my_uart.h"
#include "Gray.h"
#include "motor_app.h"
#include "mpu6050_app.h"
#include "oled_app.h"
#include "main.h"
#include <math.h>
```

## 🎯 整理效果

### 消除的重复声明
```c
// 之前在oled_app.c中重复声明
extern uint8_t sys_mode, pre_mode;  // ❌ 重复
extern uint8_t gray_buff[8];        // ❌ 重复
extern float Pitch, Roll, Yaw;     // ❌ 重复

// 现在统一在mydefine.h中声明
// 全局变量已在mydefine.h中声明  // ✅ 统一管理
```

### 代码维护优势
- ✅ **单一来源**: 所有全局变量声明在一个文件中
- ✅ **易于维护**: 添加新全局变量只需修改mydefine.h
- ✅ **避免错误**: 消除重复声明导致的不一致
- ✅ **清晰结构**: 按功能模块分类，便于理解

## 📱 使用指南

### 添加新全局变量
1. **在对应模块中定义变量**:
   ```c
   // 在module.c中
   uint8_t new_global_var = 0;
   ```

2. **在mydefine.h中添加声明**:
   ```c
   // 在mydefine.h中
   extern uint8_t new_global_var;
   ```

3. **在需要使用的模块中包含mydefine.h**:
   ```c
   #include "mydefine.h"
   ```

### 模块开发规范
1. **包含mydefine.h**: 所有模块都应包含mydefine.h
2. **避免重复声明**: 不要在模块中重复声明全局变量
3. **按功能分类**: 新增全局变量按功能分类添加到mydefine.h
4. **注释说明**: 为全局变量添加清晰的注释说明

## 🔄 系统架构

### 全局变量访问流程
```
模块A ──┐
        ├──→ mydefine.h ──→ 全局变量声明
模块B ──┤                      ↓
        │                  统一访问
模块C ──┘                      ↓
                          实际变量定义
                         (在各自模块中)
```

### 编译依赖关系
```
mydefine.h (全局变量声明)
    ↓
各模块.c (包含mydefine.h)
    ↓
编译器 (链接所有模块)
    ↓
最终程序 (全局变量正确链接)
```

## 📊 整理前后对比

### 整理前
- ❌ **分散声明**: 全局变量声明分散在各个模块
- ❌ **重复声明**: 同一变量在多个文件中声明
- ❌ **维护困难**: 修改变量需要在多处修改
- ❌ **容易出错**: 声明不一致导致编译错误

### 整理后
- ✅ **集中管理**: 所有声明集中在mydefine.h
- ✅ **避免重复**: 每个变量只声明一次
- ✅ **易于维护**: 修改变量只需修改一处
- ✅ **结构清晰**: 按功能分类，便于理解

## 🎮 实际应用

### OLED显示模块
```c
// oled_app.c中直接使用全局变量
void OLED_Display_System_Status(void)
{
    oled_printf(0, 0, "System Status");
    oled_printf(0, 1, "Mode:%d Pre:%d", sys_mode, pre_mode);  // 直接使用
    // ...
}
```

### 循迹控制模块
```c
// scheduler.c中直接使用全局变量
void gray_task()
{
    Gray_get(gray_buff);  // 直接使用全局缓冲区
    // ...
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**整理状态**: ✅ 头文件和全局变量整理完成  
**维护建议**: 🔧 新增全局变量请按规范添加到mydefine.h
