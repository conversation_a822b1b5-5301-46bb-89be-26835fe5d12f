# ⏱️ 微秒级延时完整实现文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 微秒级延时功能完成
- **目的**: 提供高精度微秒级延时功能

## 🎯 实现原理

### DWT (Data Watchpoint and Trace) 单元
STM32F407内置DWT单元，提供高精度计时功能：
- **CYCCNT寄存器**: 32位循环计数器，每个CPU时钟周期+1
- **时钟频率**: 168MHz (STM32F407)
- **理论精度**: 1/168MHz ≈ 6ns
- **实际精度**: 约1微秒 (考虑指令执行时间)

### 核心寄存器
```c
#define DWT_CTRL    (*(volatile uint32_t*)0xE0001000)  // DWT控制寄存器
#define DWT_CYCCNT  (*(volatile uint32_t*)0xE0001004)  // DWT循环计数器
#define DEM_CR      (*(volatile uint32_t*)0xE000EDFC)  // 调试异常和监控控制寄存器
#define DEM_CR_TRCENA (1 << 24)                       // 跟踪使能位
```

## 🔧 完整实现代码

### delay_us.h - 头文件
```c
#ifndef DELAY_US_H
#define DELAY_US_H

#include "main.h"

// 微秒延时函数
void delay_us_init(void);
void delay_us(uint32_t us);
uint32_t get_us_tick(void);
void delay_ns(uint32_t ns);

// 高精度计时函数
void start_timer_us(void);
uint32_t get_timer_us(void);

#endif
```

### delay_us.c - 实现文件
```c
#include "delay_us.h"

// DWT寄存器定义
#define DWT_CTRL    (*(volatile uint32_t*)0xE0001000)
#define DWT_CYCCNT  (*(volatile uint32_t*)0xE0001004)
#define DEM_CR      (*(volatile uint32_t*)0xE000EDFC)
#define DEM_CR_TRCENA (1 << 24)

// 系统时钟频率 (STM32F407通常为168MHz)
static uint32_t cpu_freq_mhz = 168;
static uint32_t timer_start = 0;

/**
 * @brief 初始化微秒延时功能
 */
void delay_us_init(void)
{
    // 获取系统时钟频率
    cpu_freq_mhz = HAL_RCC_GetHCLKFreq() / 1000000;
    
    // 使能DWT
    DEM_CR |= DEM_CR_TRCENA;
    
    // 使能CYCCNT
    DWT_CTRL |= 1;
    
    // 清零计数器
    DWT_CYCCNT = 0;
}

/**
 * @brief 微秒级延时
 * @param us 延时时间(微秒)
 */
void delay_us(uint32_t us)
{
    uint32_t start_tick = DWT_CYCCNT;
    uint32_t delay_ticks = us * cpu_freq_mhz;
    
    while((DWT_CYCCNT - start_tick) < delay_ticks);
}

/**
 * @brief 纳秒级延时 (最小精度约6ns@168MHz)
 * @param ns 延时时间(纳秒)
 */
void delay_ns(uint32_t ns)
{
    uint32_t start_tick = DWT_CYCCNT;
    uint32_t delay_ticks = (ns * cpu_freq_mhz) / 1000;
    
    // 最小延时为1个时钟周期
    if(delay_ticks == 0) delay_ticks = 1;
    
    while((DWT_CYCCNT - start_tick) < delay_ticks);
}

/**
 * @brief 获取当前微秒时间戳
 * @return 微秒时间戳
 */
uint32_t get_us_tick(void)
{
    return DWT_CYCCNT / cpu_freq_mhz;
}

/**
 * @brief 开始高精度计时
 */
void start_timer_us(void)
{
    timer_start = DWT_CYCCNT;
}

/**
 * @brief 获取计时时间
 * @return 从start_timer_us()开始的微秒数
 */
uint32_t get_timer_us(void)
{
    return (DWT_CYCCNT - timer_start) / cpu_freq_mhz;
}
```

## 📊 功能特性

### 延时功能
- ✅ **delay_us(us)**: 微秒级延时 (1-4294967295微秒)
- ✅ **delay_ns(ns)**: 纳秒级延时 (理论最小6ns)
- ✅ **高精度**: 基于CPU时钟周期，精度极高
- ✅ **无中断影响**: 不受中断影响的精确延时

### 计时功能
- ✅ **get_us_tick()**: 获取当前微秒时间戳
- ✅ **start_timer_us()**: 开始计时
- ✅ **get_timer_us()**: 获取计时时间
- ✅ **高精度测量**: 可测量微秒级时间间隔

## 🎯 使用示例

### 基础延时
```c
// 初始化 (在main或scheduler_init中调用)
delay_us_init();

// 延时10微秒
delay_us(10);

// 延时500纳秒
delay_ns(500);

// 延时1毫秒 (1000微秒)
delay_us(1000);
```

### 高精度计时
```c
// 测量代码执行时间
start_timer_us();
// 执行需要测量的代码
some_function();
uint32_t execution_time = get_timer_us();
printf("Execution time: %lu us\n", execution_time);
```

### 精确脉冲生成
```c
// 生成精确的PWM脉冲
void generate_precise_pulse(uint32_t high_us, uint32_t low_us)
{
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_0, GPIO_PIN_SET);
    delay_us(high_us);
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_0, GPIO_PIN_RESET);
    delay_us(low_us);
}

// 生成频率为1kHz，占空比50%的脉冲
generate_precise_pulse(500, 500);  // 500us高电平 + 500us低电平 = 1ms周期
```

### 传感器时序控制
```c
// DHT22传感器时序控制示例
void dht22_start_signal(void)
{
    // 主机拉低18ms
    HAL_GPIO_WritePin(DHT22_PORT, DHT22_PIN, GPIO_PIN_RESET);
    delay_us(18000);
    
    // 主机拉高20-40us
    HAL_GPIO_WritePin(DHT22_PORT, DHT22_PIN, GPIO_PIN_SET);
    delay_us(30);
    
    // 切换为输入模式等待响应
    // ...
}
```

## 📱 性能特性

### 精度分析
```
CPU频率: 168MHz
时钟周期: 1/168MHz ≈ 6ns
理论精度: 6ns
实际精度: 约1us (考虑指令执行)

延时范围:
- 最小延时: 约1us
- 最大延时: 4294967295us (约71分钟)
- 精度: ±1us
```

### 性能对比
```
HAL_Delay():
- 精度: 1ms
- 基于: SysTick中断
- 影响: 受中断影响

delay_us():
- 精度: 1us
- 基于: DWT硬件计数器
- 影响: 不受中断影响
```

## 🔍 注意事项

### 使用限制
1. **仅适用于Cortex-M4**: STM32F407等Cortex-M4内核
2. **需要初始化**: 必须先调用delay_us_init()
3. **阻塞延时**: 延时期间CPU被占用，无法执行其他任务
4. **时钟依赖**: 依赖于系统时钟频率，时钟变化会影响精度

### 最佳实践
1. **短延时使用**: 适合微秒级短延时，长延时建议用HAL_Delay()
2. **关键时序**: 适合对时序要求严格的场合
3. **测量工具**: 适合作为高精度时间测量工具
4. **避免长时间**: 避免毫秒级以上的长时间延时

## 🚀 应用场景

### 硬件接口时序
```c
// SPI软件模拟
void spi_write_bit(uint8_t bit)
{
    HAL_GPIO_WritePin(SPI_MOSI_PORT, SPI_MOSI_PIN, bit ? GPIO_PIN_SET : GPIO_PIN_RESET);
    delay_us(1);  // 建立时间
    HAL_GPIO_WritePin(SPI_CLK_PORT, SPI_CLK_PIN, GPIO_PIN_SET);
    delay_us(1);  // 时钟高电平时间
    HAL_GPIO_WritePin(SPI_CLK_PORT, SPI_CLK_PIN, GPIO_PIN_RESET);
    delay_us(1);  // 时钟低电平时间
}
```

### 传感器控制
```c
// 超声波传感器HC-SR04
uint32_t hcsr04_read_distance(void)
{
    // 发送10us触发脉冲
    HAL_GPIO_WritePin(TRIG_PORT, TRIG_PIN, GPIO_PIN_SET);
    delay_us(10);
    HAL_GPIO_WritePin(TRIG_PORT, TRIG_PIN, GPIO_PIN_RESET);
    
    // 测量回波时间
    start_timer_us();
    while(HAL_GPIO_ReadPin(ECHO_PORT, ECHO_PIN) == GPIO_PIN_RESET);
    uint32_t start_time = get_timer_us();
    
    while(HAL_GPIO_ReadPin(ECHO_PORT, ECHO_PIN) == GPIO_PIN_SET);
    uint32_t echo_time = get_timer_us() - start_time;
    
    // 计算距离 (声速340m/s)
    return echo_time * 340 / 2 / 10000;  // 单位: cm
}
```

### 性能测试
```c
// 测试函数执行时间
void performance_test(void)
{
    start_timer_us();
    
    // 测试灰度传感器读取时间
    Gray_get(gray_buff);
    uint32_t gray_time = get_timer_us();
    
    start_timer_us();
    // 测试MPU6050读取时间
    mpu_dmp_get_data(&Pitch, &Roll, &Yaw);
    uint32_t mpu_time = get_timer_us();
    
    printf("Gray read: %lu us, MPU read: %lu us\n", gray_time, mpu_time);
}
```

## 📊 集成建议

### 初始化集成
```c
void system_init(void)
{
    HAL_Init();
    SystemClock_Config();
    
    // 初始化微秒延时 (尽早初始化)
    delay_us_init();
    
    // 其他初始化...
}
```

### 任务调度集成
```c
// 在需要精确时序的任务中使用
void precise_control_task(void)
{
    // 精确的电机控制时序
    HAL_GPIO_WritePin(MOTOR_EN_PORT, MOTOR_EN_PIN, GPIO_PIN_SET);
    delay_us(50);  // 使能建立时间
    
    // 设置PWM
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, pwm_value);
    delay_us(10);  // PWM建立时间
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**功能状态**: ✅ 微秒级延时功能完成  
**精度**: ⏱️ 1微秒精度，基于DWT硬件计数器
