# 🏗️ STM32F407循迹小车技术架构总览

## 📋 文档信息
- **文档类型**: 技术架构总览
- **版本**: v2.0
- **创建日期**: 2025-07-07
- **维护团队**: STM32循迹小车开发团队
- **适用对象**: 开发人员、系统架构师

## 🎯 架构概述

### 设计理念
本系统采用分层架构设计，遵循模块化、可扩展、可维护的设计原则，基于STM32F407微控制器构建智能循迹小车系统。

### 核心特性
- **🏗️ 分层架构**: 硬件抽象层、驱动层、应用层、调试层
- **⚡ 任务调度**: 基于时间片的多任务调度系统
- **🔧 模块化设计**: 基于motor_driver架构的底层驱动
- **📊 实时监控**: 完整的调试和监控体系
- **🎯 智能控制**: 7路传感器循迹算法

## 🏗️ 系统架构

### 整体架构图
```
STM32F407循迹小车系统架构
┌─────────────────────────────────────────────────────────────┐
│                        调试层 (Debug Layer)                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   串口监控       │   纸飞机工具     │      LED指示            │
│   实时状态显示   │   实时曲线绘制   │      状态和模式显示      │
└─────────────────┴─────────────────┴─────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                       应用层 (Application Layer)             │
├─────────────────┬─────────────────┬─────────────────────────┤
│   任务调度器     │   运动控制       │      模式管理           │
│   scheduler.c   │   motion_control │      4种运行模式        │
│   多任务管理     │   高级运动接口   │                        │
└─────────────────┴─────────────────┴─────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        驱动层 (Driver Layer)                 │
├─────────────────┬─────────────────┬─────────────────────────┤
│   电机驱动       │   传感器驱动     │      通信驱动           │
│   car.c         │   Gray.c        │      my_uart.c         │
│   motor_driver  │   7路灰度传感器  │      DMA串口           │
│   架构          │                 │                        │
└─────────────────┴─────────────────┴─────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    硬件抽象层 (HAL Layer)                     │
├─────────────────┬─────────────────┬─────────────────────────┤
│   STM32F407VET6 │   时钟系统       │      外设配置           │
│   主控芯片       │   168MHz        │      GPIO/TIM/UART     │
│                 │                 │      DMA配置           │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 架构层次说明

#### 1. 硬件抽象层 (HAL Layer)
- **主控芯片**: STM32F407VET6
- **时钟系统**: 168MHz系统时钟
- **外设配置**: GPIO、定时器、UART、DMA
- **硬件资源**: 管理所有硬件资源

#### 2. 驱动层 (Driver Layer)
- **电机驱动**: 基于motor_driver架构的电机控制
- **传感器驱动**: 7路灰度传感器数据采集
- **按键驱动**: 3键控制系统
- **通信驱动**: DMA串口通信

#### 3. 应用层 (Application Layer)
- **任务调度器**: 多任务时间片调度
- **运动控制**: 高级运动控制接口
- **模式管理**: 4种运行模式管理

#### 4. 调试层 (Debug Layer)
- **串口监控**: 实时状态显示
- **纸飞机工具**: 实时曲线绘制
- **LED指示**: 状态和模式指示

## 🔧 核心模块架构

### 任务调度系统架构
```c
// 任务调度器核心结构
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;          // 执行周期(ms)
    uint32_t last_run;         // 上次执行时间
} task_t;

// 任务配置表
static task_t scheduler_task[] = {
    {car_task,20,0},        // 车辆控制任务 - 20ms周期
    {key_task,10,0},        // 按键处理任务 - 10ms周期
    {uart_task,150,0},      // 串口通信任务 - 150ms周期
    {gray_task,30,0},       // 灰度传感器任务 - 30ms周期
};

// 调度器执行流程
void scheduler_run(void) {
    for(int i = 0; i < task_num; i++) {
        if(HAL_GetTick() - scheduler_task[i].last_run >= scheduler_task[i].rate_ms) {
            scheduler_task[i].task_func();
            scheduler_task[i].last_run = HAL_GetTick();
        }
    }
}
```

### 电机驱动架构 (基于motor_driver)
```c
// 电机驱动架构层次
┌─────────────────────────────────────────┐
│            高级运动控制接口               │
│  move_forward() move_backward()         │
│  turn_left() turn_right() stop_motors() │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            电机实体管理层                │
│  Motor_SetSpeed() Motor_Stop()          │
│  Motor_GetState() Motor_Enable()        │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            硬件抽象层                   │
│  PWM控制 GPIO方向控制 编码器反馈        │
└─────────────────────────────────────────┘
```

### 传感器系统架构
```c
// 传感器数据流
┌─────────────────────────────────────────┐
│            物理传感器层                  │
│  P_2~P_8 七路灰度传感器                 │
└─────────────────┬───────────────────────┘
                  │ GPIO读取
┌─────────────────▼───────────────────────┐
│            数据采集层                   │
│  gray_task() 30ms周期采集               │
└─────────────────┬───────────────────────┘
                  │ 数据处理
┌─────────────────▼───────────────────────┐
│            算法处理层                   │
│  循迹算法 位置计算 运动决策             │
└─────────────────┬───────────────────────┘
                  │ 控制输出
┌─────────────────▼───────────────────────┐
│            运动控制层                   │
│  速度控制 方向控制 声光控制             │
└─────────────────────────────────────────┘
```

## 📊 数据流架构

### 系统数据流图
```
传感器数据 ──┐
            ├──► 循迹算法 ──► 运动控制 ──► 电机驱动 ──► 物理运动
编码器数据 ──┘                    │
                                 ▼
按键输入 ────► 模式管理 ──────► 状态管理
                                 │
                                 ▼
                            串口输出 ──► 调试监控
                                 │
                                 ▼
                            纸飞机工具 ──► 实时曲线
```

### 关键数据结构
```c
// 电机状态数据
typedef struct {
    Motor_t left_motor;             // 左电机实体
    Motor_t right_motor;            // 右电机实体
    uint8_t initialized;            // 初始化标志
} Car_t;

// 传感器数据
uint8_t gray_buff[8];              // 传感器原始数据
int16_t line_position;             // 线位置计算结果

// 系统状态数据
uint8_t sys_mode;                  // 当前运行模式
uint8_t sys_ture;                  // 系统状态标志
float global_rpm_l, global_rpm_r;  // 全局RPM数据
```

## 🔄 控制流架构

### 主控制循环
```c
int main(void) {
    // 1. 硬件初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_TIM_Init();
    MX_UART_Init();
    
    // 2. 应用初始化
    Car_Init();                    // 电机驱动初始化
    scheduler_init();              // 任务调度器初始化
    
    // 3. 主循环
    while (1) {
        scheduler_run();           // 任务调度执行
    }
}
```

### 任务执行流程
```
系统启动
    │
    ▼
硬件初始化 ──► 应用初始化 ──► 主循环开始
                              │
                              ▼
                         任务调度器
                              │
                ┌─────────────┼─────────────┐
                ▼             ▼             ▼
            car_task      key_task      uart_task
            (20ms)        (10ms)        (150ms)
                │             │             │
                └─────────────┼─────────────┘
                              ▼
                          gray_task
                          (30ms)
                              │
                              ▼
                         循环执行
```

## 🎯 性能特性

### 实时性能指标
- **任务调度精度**: ±1ms
- **传感器响应时间**: <30ms
- **电机控制延迟**: <20ms
- **串口数据更新**: 150ms周期
- **循迹算法执行时间**: <5ms

### 资源使用情况
- **Flash使用**: ~64KB
- **RAM使用**: ~8KB
- **CPU使用率**: <50%
- **任务数量**: 4个
- **中断优先级**: 3级

### 扩展性设计
- **模块化接口**: 便于功能扩展
- **参数化配置**: 支持参数调整
- **硬件抽象**: 便于硬件移植
- **调试接口**: 完整的调试体系

## 🔧 开发和维护

### 代码组织结构
```
tracking_car/
├── Core/                      # STM32 HAL核心文件
│   ├── Inc/                   # 头文件
│   └── Src/                   # 源文件
├── APP/                       # 应用层代码
│   ├── car.c/h               # 电机驱动
│   ├── Gray.c/h              # 传感器驱动
│   ├── key.c/h               # 按键驱动
│   ├── my_uart.c/h           # 串口驱动
│   ├── scheduler.c/h         # 任务调度
│   └── motion_control.h      # 运动控制接口
├── docs/                      # 项目文档
└── motor_driver/             # 参考架构
```

### 开发工具链
- **IDE**: Keil MDK-ARM v5.06+
- **编译器**: ARM Compiler v5.06
- **调试器**: ST-Link SWD
- **版本控制**: Git
- **文档工具**: Markdown

### 质量保证
- **代码规范**: 统一的编码风格
- **模块测试**: 单元测试和集成测试
- **性能测试**: 实时性能验证
- **文档维护**: 完整的技术文档

---

**文档版本**: v2.0  
**最后更新**: 2025-07-07  
**技术支持**: STM32循迹小车开发团队
