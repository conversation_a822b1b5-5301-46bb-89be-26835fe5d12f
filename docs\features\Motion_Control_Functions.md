# 🚗 运动控制函数库指南

## 📊 功能概述

实现了完整的双电机运动控制函数库：
- **基础运动**: 前进、后退、左转、右转、停止
- **自定义控制**: 完全自定义的双电机速度控制
- **快捷宏定义**: 预定义的速度等级和运动模式
- **高级运动**: 原地转弯、大弯转向等复杂运动

## 🎯 基础运动函数

### **1. 前进运动**
```c
void move_forward(int8_t speed);

// 使用示例
move_forward(50);  // 50%速度前进
move_forward(80);  // 80%速度前进
```

**功能**: 双电机同速前进
**参数**: speed (1-99)
**效果**: 直线前进运动

### **2. 后退运动**
```c
void move_backward(int8_t speed);

// 使用示例
move_backward(30);  // 30%速度后退
move_backward(60);  // 60%速度后退
```

**功能**: 双电机同速后退
**参数**: speed (1-99)
**效果**: 直线后退运动

### **3. 左转运动**
```c
void turn_left(int8_t speed);

// 使用示例
turn_left(40);  // 40%基础速度左转
turn_left(70);  // 70%基础速度左转
```

**功能**: 差速转向左转
**参数**: speed (1-99) 基础速度
**效果**: 左轮半速，右轮全速

### **4. 右转运动**
```c
void turn_right(int8_t speed);

// 使用示例
turn_right(40);  // 40%基础速度右转
turn_right(70);  // 70%基础速度右转
```

**功能**: 差速转向右转
**参数**: speed (1-99) 基础速度
**效果**: 左轮全速，右轮半速

### **5. 停止运动**
```c
void stop_motors(void);

// 使用示例
stop_motors();  // 立即停止所有电机
```

**功能**: 停止所有电机
**参数**: 无
**效果**: 双电机立即停止

### **6. 自定义控制**
```c
void move_custom(int8_t left_speed, int8_t right_speed);

// 使用示例
move_custom(50, 30);   // 左轮50%，右轮30%
move_custom(-40, 60);  // 左轮后退40%，右轮前进60%
move_custom(0, 80);    // 左轮停止，右轮前进80%
```

**功能**: 完全自定义双电机控制
**参数**: left_speed (-99到+99), right_speed (-99到+99)
**效果**: 任意组合的双电机控制

## 🚀 快捷宏定义

### **预定义速度等级**
```c
#define SPEED_SLOW    20    // 慢速
#define SPEED_MEDIUM  50    // 中速
#define SPEED_FAST    80    // 快速
#define SPEED_MAX     99    // 最大速度
```

### **快捷运动宏**
```c
// 前进宏
FORWARD_SLOW();     // 慢速前进
FORWARD_MEDIUM();   // 中速前进
FORWARD_FAST();     // 快速前进

// 后退宏
BACKWARD_SLOW();    // 慢速后退
BACKWARD_MEDIUM();  // 中速后退
BACKWARD_FAST();    // 快速后退

// 转向宏
LEFT_SLOW();        // 慢速左转
LEFT_MEDIUM();      // 中速左转
LEFT_FAST();        // 快速左转

RIGHT_SLOW();       // 慢速右转
RIGHT_MEDIUM();     // 中速右转
RIGHT_FAST();       // 快速右转

// 停止宏
STOP();             // 停止
```

## 🎮 高级运动控制

### **原地转弯**
```c
SPIN_LEFT(50);      // 原地左转 (左轮后退，右轮前进)
SPIN_RIGHT(50);     // 原地右转 (左轮前进，右轮后退)

// 等效于
move_custom(-50, 50);   // 原地左转
move_custom(50, -50);   // 原地右转
```

### **大弯转向**
```c
CURVE_LEFT(60);     // 大弯左转 (左轮1/3速度，右轮全速)
CURVE_RIGHT(60);    // 大弯右转 (左轮全速，右轮1/3速度)

// 等效于
move_custom(20, 60);    // 大弯左转
move_custom(60, 20);    // 大弯右转
```

## 📈 使用示例

### **基础运动序列**
```c
// 前进2秒
move_forward(50);
HAL_Delay(2000);

// 左转1秒
turn_left(40);
HAL_Delay(1000);

// 前进1秒
move_forward(50);
HAL_Delay(1000);

// 停止
stop_motors();
```

### **复杂运动序列**
```c
// 启动序列
FORWARD_MEDIUM();       // 中速前进
HAL_Delay(3000);

// 避障序列
RIGHT_FAST();           // 快速右转
HAL_Delay(500);
FORWARD_SLOW();         // 慢速前进
HAL_Delay(1000);
LEFT_FAST();            // 快速左转
HAL_Delay(500);

// 原地调头
SPIN_RIGHT(60);         // 原地右转
HAL_Delay(2000);

// 停止
STOP();
```

### **自定义运动模式**
```c
// S形运动
move_custom(60, 30);    // 右弯
HAL_Delay(1000);
move_custom(30, 60);    // 左弯
HAL_Delay(1000);
move_custom(60, 30);    // 右弯
HAL_Delay(1000);

// 8字形运动
for(int i = 0; i < 4; i++) {
    CURVE_LEFT(50);     // 左大弯
    HAL_Delay(1000);
}
for(int i = 0; i < 4; i++) {
    CURVE_RIGHT(50);    // 右大弯
    HAL_Delay(1000);
}
```

## 🔧 运动特性分析

### **转向半径对比**
```c
// 不同转向方式的转向半径
turn_left(50);          // 中等转向半径
CURVE_LEFT(50);         // 大转向半径 (更平缓)
SPIN_LEFT(50);          // 零转向半径 (原地转)
```

### **速度与转向关系**
```c
// 速度越高，转向半径越大
turn_left(20);          // 小半径左转
turn_left(50);          // 中半径左转
turn_left(80);          // 大半径左转
```

### **电机负载分析**
```c
// 不同运动模式的电机负载
move_forward(50);       // 双电机均匀负载
turn_left(50);          // 右电机负载大，左电机负载小
SPIN_LEFT(50);          // 双电机负载均匀但方向相反
```

## ⚠️ 使用注意事项

### **速度参数范围**
- **有效范围**: 1-99
- **推荐范围**: 20-80 (避免过低或过高)
- **安全考虑**: 首次使用建议从低速开始

### **运动切换**
- **平滑切换**: 运动模式切换前建议短暂停止
- **避免冲击**: 不要在高速时突然改变方向
- **渐进调整**: 速度变化建议渐进式

### **机械限制**
- **转向空间**: 确保有足够的转向空间
- **地面条件**: 不同地面摩擦力影响转向效果
- **电池电压**: 低电压时运动性能下降

## 📊 性能优化建议

### **运动平滑性**
```c
// 平滑加速
for(int speed = 20; speed <= 60; speed += 10) {
    move_forward(speed);
    HAL_Delay(200);
}

// 平滑减速
for(int speed = 60; speed >= 20; speed -= 10) {
    move_forward(speed);
    HAL_Delay(200);
}
stop_motors();
```

### **精确转向**
```c
// 精确90度转向 (需要根据实际调整时间)
turn_right(50);
HAL_Delay(850);  // 根据实际测试调整
stop_motors();
```

### **能耗优化**
```c
// 使用适中速度，避免过高功耗
#define ECO_SPEED 35

move_forward(ECO_SPEED);    // 经济模式前进
turn_left(ECO_SPEED);       // 经济模式转向
```

---

**创建时间**: 2025-06-20  
**功能**: 完整的运动控制函数库  
**特色**: 基础运动 + 高级控制 + 快捷宏  
**状态**: ✅ 已实现
