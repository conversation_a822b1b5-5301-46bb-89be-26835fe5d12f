# 🚗 循迹小车速度控制优化文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: Alex (工程师)
- **状态**: 已完成
- **目的**: 优化小车启动力度，提升控制精度

## 🎯 优化目标

### 问题描述
- **启动力度过大**: 小车启动时冲击过强
- **循迹反应过激**: 转向调整幅度过大
- **控制精度不足**: 难以实现精确的线路跟踪

### 优化目标
- **平滑启动**: 减少启动冲击，提升舒适性
- **精确控制**: 优化循迹算法，提升跟踪精度
- **稳定运行**: 减少震荡，提升系统稳定性

## 🔧 优化方案

### 1. 基础速度参数调整

#### 模式1 - 直线检测模式
```c
// 优化前
move_forward(35);  // 启动力度过大

// 优化后
move_forward(25);  // 降低启动速度，减少冲击
```

#### 模式2 - 循迹模式
```c
// 优化前
uint8_t base_speed = 28;  // 基础速度过高
uint8_t turn_speed = 15;  // 转向速度过高

// 优化后
uint8_t base_speed = 20;  // 降低基础速度
uint8_t turn_speed = 12;  // 降低转向速度
```

#### 测试模式
```c
// 优化前
move_forward(40);  // 测试速度过高
turn_right(40);
move_backward(40);
turn_left(40);

// 优化后
move_forward(25);  // 降低测试速度
turn_right(25);
move_backward(25);
turn_left(25);
```

### 2. 循迹控制精度优化

#### 转向调整幅度优化
```c
// 优化前 - 调整幅度过大
if(gray_buff[2] == 0) {
    move_custom(base_speed + 8, turn_speed);  // 速度差异过大
} else if(gray_buff[3] == 0) {
    move_custom(base_speed + 4, base_speed - 4);  // 调整幅度过大
}

// 优化后 - 更温和的调整
if(gray_buff[2] == 0) {
    move_custom(base_speed + 5, turn_speed);  // 减小速度差异
} else if(gray_buff[3] == 0) {
    move_custom(base_speed + 2, base_speed - 2);  // 减小调整幅度
}
```

### 3. 渐进启动功能

#### 实现原理
```c
void move_forward(int8_t speed) {
    static int8_t last_speed = 0;
    int8_t target_speed = speed;
    
    // 渐进启动逻辑
    if(abs(target_speed - last_speed) > 10) {
        if(target_speed > last_speed) {
            speed = last_speed + 8;  // 渐进加速
        } else {
            speed = last_speed - 8;  // 渐进减速
        }
    }
    
    last_speed = speed;
    Motor_SetSpeed(&car.left_motor, speed);
    Motor_SetSpeed(&car.right_motor, speed);
}
```

#### 优势特点
- **平滑加速**: 避免突然的速度变化
- **减少冲击**: 降低机械冲击和震动
- **提升精度**: 更好的速度控制精度

## 📊 优化效果对比

### 速度参数对比表

| 参数 | 优化前 | 优化后 | 改善幅度 |
|------|--------|--------|----------|
| 模式1直行速度 | 35 | 25 | -28.6% |
| 循迹基础速度 | 28 | 20 | -28.6% |
| 循迹转向速度 | 15 | 12 | -20.0% |
| 测试模式速度 | 40 | 25 | -37.5% |
| 大幅转向调整 | ±8 | ±5 | -37.5% |
| 微调转向调整 | ±4 | ±2 | -50.0% |

### 性能改善预期

#### 启动性能
- **冲击减少**: 降低37.5%的启动冲击
- **平滑度提升**: 渐进启动机制
- **控制精度**: 提升速度控制精度

#### 循迹性能
- **跟踪精度**: 减少50%的过度调整
- **稳定性**: 降低系统震荡
- **响应速度**: 保持快速响应能力

#### 系统稳定性
- **减少震荡**: 更温和的控制策略
- **延长寿命**: 减少机械磨损
- **能耗优化**: 降低不必要的能耗

## 🎮 运行模式优化

### 模式0 - 停止模式
- **无变化**: 保持停止状态
- **响应**: 立即响应停止指令

### 模式1 - 直线检测模式
- **启动速度**: 25 (降低28.6%)
- **检测精度**: 保持原有精度
- **停止响应**: 立即停止

### 模式2 - 循迹模式
- **基础速度**: 20 (降低28.6%)
- **转向控制**: 更精确的调整
- **稳定性**: 显著提升

### 模式3 - 预留模式
- **保持**: 为未来扩展预留

## 🔧 调试和验证

### 验证方法

#### 1. 启动测试
```
测试步骤:
1. 将小车放在起始位置
2. 选择模式1或模式2
3. 观察启动过程
4. 记录启动冲击程度

预期结果:
- 启动更平滑
- 冲击明显减少
- 无明显震动
```

#### 2. 循迹精度测试
```
测试步骤:
1. 准备标准测试赛道
2. 运行模式2循迹
3. 观察跟踪精度
4. 记录偏离程度

预期结果:
- 跟踪更精确
- 减少过度调整
- 路径更平滑
```

#### 3. 稳定性测试
```
测试步骤:
1. 长时间运行测试
2. 观察系统稳定性
3. 记录异常情况
4. 分析性能数据

预期结果:
- 系统更稳定
- 减少震荡
- 持续性能良好
```

### 调试参数

#### 可调节参数
```c
// 基础速度参数
uint8_t base_speed = 20;     // 可调范围: 15-30
uint8_t turn_speed = 12;     // 可调范围: 8-18

// 渐进启动参数
int8_t accel_step = 8;       // 可调范围: 5-12
int8_t speed_threshold = 10; // 可调范围: 8-15

// 转向调整参数
int8_t major_adjust = 5;     // 可调范围: 3-8
int8_t minor_adjust = 2;     // 可调范围: 1-4
```

#### 调试建议
1. **逐步调整**: 每次只调整一个参数
2. **充分测试**: 每次调整后进行完整测试
3. **记录数据**: 记录调整效果和性能数据
4. **找到平衡**: 在速度和精度间找到最佳平衡

## 📈 性能监控

### 监控指标
- **启动时间**: 从静止到稳定速度的时间
- **跟踪精度**: 偏离中心线的距离
- **响应时间**: 检测到偏差后的调整时间
- **稳定性**: 系统震荡的频率和幅度

### 数据采集
```c
// 通过串口监控性能数据
my_printf(&huart1, "Speed: L:%d R:%d | Sensors: %d%d%d%d%d%d%d", 
          left_speed, right_speed,
          gray_buff[1], gray_buff[2], gray_buff[3], gray_buff[4],
          gray_buff[5], gray_buff[6], gray_buff[7]);

// 通过纸飞机工具查看速度曲线
PRINT(speed, "%.2f,%.2f", fabs(speed_l), fabs(speed_r));
```

## 🎯 未来优化方向

### 1. PID控制
- 实现闭环速度控制
- 提升控制精度
- 减少稳态误差

### 2. 自适应控制
- 根据路况自动调整参数
- 智能优化控制策略
- 提升适应性

### 3. 机器学习
- 学习最优控制参数
- 自动优化性能
- 持续改进

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**技术支持**: STM32循迹小车开发团队
