# 🔧 电机不动问题排查指南

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 电机问题排查和调试指南
- **目的**: 快速定位和解决电机不动问题

## 🎯 问题分析

### 当前状态
```
串口输出: [M0] 1111111 | Err:0.00 | Act:0 | P:0.1 R:0.1 Y:0.1 | YErr:0.0 | Dir:0
问题: 电机不动，按了按键
分析: [M0] 表示当前在模式0（停止模式）
```

### 可能原因
1. **模式问题**: 当前在模式0，电机被强制停止
2. **按键问题**: 按键没有正确切换模式
3. **电机硬件**: 电机驱动或连接问题
4. **初始化问题**: 电机初始化失败

## 🔍 排查步骤

### 1. 检查当前模式和按键状态
```
新增调试信息:
[M0] 1111111 | Err:0.00 | Act:0 | P:0.1 R:0.1 Y:0.1 | YErr:0.0 | Dir:0 | Pre:0 Ture:0

字段说明:
- [M0]: 当前模式 (0=停止, 1=直线, 2=循迹)
- Pre:0: 预选模式
- Ture:0: 启动标志 (0=未启动, 1=启动)
```

### 2. 按键操作流程
```
正确的按键操作:
1. 按KEY0或KEY1: 选择预选模式 (Pre值会变化)
2. 按KEY2: 确认执行 (sys_mode变为pre_mode, sys_ture变为1)

按键映射:
- KEY0 (key0_Pin): 预选模式正序切换 (0→1→2→3→0)
- KEY1 (key1_Pin): 预选模式倒序切换 (3→2→1→0→3)  
- KEY2 (key2_Pin): 确认执行预选模式
```

### 3. 模式功能说明
```c
switch(sys_mode) {
    case 0:  // 模式0：停止模式
        Motor_App_Stop();  // 强制停止电机
        break;
        
    case 1:  // 模式1：直线行驶
        if(active_sensors > 0) {
            Motor_App_Stop();  // 遇黑线停止
        } else {
            Motor_App_Forward(25);  // 直线前进
        }
        break;
        
    case 2:  // 模式2：循迹模式
        // 复杂的循迹控制逻辑
        Motor_App_Set_Speed(left_speed, right_speed);
        break;
}
```

## 🔧 解决方案

### 方案1: 检查按键操作
1. **选择模式**: 按KEY0或KEY1，观察Pre值变化
2. **确认执行**: 按KEY2，观察sys_mode和sys_ture变化
3. **验证结果**: 串口输出应显示[M1]或[M2]

### 方案2: 强制设置模式
```c
// 在scheduler.c中临时添加强制模式设置
void gray_task()
{
    // 临时强制设置为模式1进行测试
    sys_mode = 1;
    sys_ture = 1;
    
    // 原有代码...
}
```

### 方案3: 检查电机硬件
```
硬件检查清单:
1. 电机供电: 确认电机驱动板供电正常
2. PWM信号: 确认TIM3_CH1/CH2输出PWM
3. 方向控制: 确认Ain1/Ain2, Bin1/Bin2连接正确
4. 使能信号: 确认电机驱动使能信号正常
```

### 方案4: 电机驱动测试
```c
// 在motor_task中添加测试代码
void motor_task()
{
    // 强制测试电机
    static uint32_t test_count = 0;
    test_count++;
    
    if(test_count % 100 == 0) {  // 每2秒测试一次
        Motor_App_Forward(30);   // 强制前进
    }
    
    Motor_App_Task();
}
```

## 📊 调试信息解读

### 正常工作状态
```
[M1] 0000000 | Err:0.00 | Act:0 | P:0.2 R:-0.1 Y:45.3 | YErr:0.0 | Dir:0 | Pre:1 Ture:1
说明: 模式1，预选模式1，已启动，电机应该前进
```

### 按键未响应
```
[M0] 1111111 | Err:0.00 | Act:0 | P:0.1 R:0.1 Y:0.1 | YErr:0.0 | Dir:0 | Pre:0 Ture:0
问题: Pre和Ture都是0，说明按键没有响应
检查: 按键硬件连接和GPIO配置
```

### 模式未切换
```
[M0] 1111111 | Err:0.00 | Act:0 | P:0.1 R:0.1 Y:0.1 | YErr:0.0 | Dir:0 | Pre:2 Ture:0
问题: Pre=2但sys_mode=0，说明没有按KEY2确认
操作: 按KEY2确认执行
```

## 🎯 快速测试方法

### 测试1: 按键功能测试
```
1. 观察串口输出的Pre值
2. 按KEY0，Pre应该变为1
3. 再按KEY0，Pre应该变为2
4. 按KEY1，Pre应该变为1
5. 按KEY2，sys_mode应该变为Pre值，Ture变为1
```

### 测试2: 强制电机测试
```c
// 在gray_task开头添加
void gray_task()
{
    // 强制测试
    sys_mode = 1;  // 设置为模式1
    sys_ture = 1;  // 设置启动标志
    
    // 原有代码...
}
```

### 测试3: 电机直接控制
```c
// 在motor_task中添加
void motor_task()
{
    // 直接控制电机测试
    Motor_Set_Speed(&left_motor, 20);
    Motor_Set_Speed(&right_motor, 20);
    
    Motor_App_Task();
}
```

## 🔌 硬件检查

### 按键硬件
```
按键连接检查:
- KEY0: key0_GPIO_Port, key0_Pin
- KEY1: key1_GPIO_Port, key1_Pin  
- KEY2: key2_GPIO_Port, key2_Pin
- 按键按下时应该是GPIO_PIN_RESET (低电平)
```

### 电机硬件
```
电机驱动连接:
- 左电机PWM: TIM3_CH1
- 右电机PWM: TIM3_CH2
- 左电机方向: Ain1_Pin, Ain2_Pin
- 右电机方向: Bin1_Pin, Bin2_Pin
- 电机供电: 确认驱动板供电正常
```

## 📱 实际操作建议

### 立即测试
1. **重新编译**: 使用新增的调试信息重新编译
2. **观察串口**: 查看Pre和Ture值的变化
3. **按键测试**: 按照正确流程操作按键
4. **模式确认**: 确认sys_mode是否正确切换

### 如果按键正常但电机不动
1. **检查模式1**: 确认在模式1下，如果没有黑线应该前进
2. **检查电机初始化**: 确认Motor_App_Init()正常执行
3. **检查PWM输出**: 使用示波器检查PWM信号
4. **检查电机驱动**: 确认电机驱动板工作正常

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**调试状态**: ✅ 已添加详细调试信息  
**下一步**: 🔧 根据串口输出进行针对性排查
