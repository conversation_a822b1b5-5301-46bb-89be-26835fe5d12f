# 🚗 STM32F407循迹小车完整系统文档

## 📋 项目概述

本项目是一个基于STM32F407微控制器的智能循迹小车系统，采用先进的任务调度架构和模块化设计，具备完整的循迹功能、多种运行模式和丰富的调试工具。

### 🎯 核心特性
- **智能循迹**: 7路灰度传感器，精确线路跟踪
- **多种模式**: 停止/直线/循环/预留四种运行模式
- **任务调度**: 基于时间片的多任务调度系统
- **实时监控**: DMA串口通信，纸飞机调试工具
- **模块化设计**: 基于motor_driver架构的底层驱动

### 🏗️ 系统架构
```
STM32F407循迹小车系统
├── 硬件层
│   ├── MG513XP28双电机 (28:1减速比)
│   ├── 7路灰度传感器 (P_2到P_8)
│   ├── 编码器反馈 (TIM1/TIM2)
│   └── 声光控制 (PF11)
├── 驱动层
│   ├── 电机驱动 (基于motor_driver架构)
│   ├── 传感器驱动 (Gray.c)
│   └── 通信驱动 (my_uart.c)
├── 应用层
│   ├── 任务调度器 (scheduler.c)
│   ├── 按键控制 (key.c)
│   └── 运动控制 (car.c)
└── 调试层
    ├── 串口监控
    ├── 纸飞机工具
    └── 实时数据显示
```

## 📁 文档结构

### 🚀 快速开始
- [`final/Quick_Start_Guide.md`](final/Quick_Start_Guide.md) - 快速入门指南
- [`final/System_Verification_Checklist.md`](final/System_Verification_Checklist.md) - 系统验证清单
- [`guides/Quick_Test_Guide.md`](guides/Quick_Test_Guide.md) - 快速测试指南

### 🔧 核心功能
- [`features/Mode_System_Overview.md`](features/Mode_System_Overview.md) - 模式系统总览
- [`features/Line_Following_Implementation.md`](features/Line_Following_Implementation.md) - 循迹实现
- [`features/Motion_Control_Functions.md`](features/Motion_Control_Functions.md) - 运动控制
- [`features/Gray_Sensor_Guide.md`](features/Gray_Sensor_Guide.md) - 灰度传感器指南

### 🏗️ 系统开发
- [`development/Car_Driver_Rewrite_Based_On_Motor_Driver.md`](development/Car_Driver_Rewrite_Based_On_Motor_Driver.md) - 电机驱动架构
- [`features/UART_DMA_Guide.md`](features/UART_DMA_Guide.md) - DMA串口通信
- [`features/PaperPlane_Curve_Plotting_Guide.md`](features/PaperPlane_Curve_Plotting_Guide.md) - 调试工具

### 🔍 问题分析
- [`analysis/`](analysis/) - 问题分析和解决方案
- [`motor/`](motor/) - 电机相关问题和优化

### 📊 完整系统
- [`final/Complete_Tracking_Car_System.md`](final/Complete_Tracking_Car_System.md) - 完整系统文档

## 🎯 技术规格

### 硬件配置
| 组件 | 型号/规格 | 数量 | 功能 |
|------|-----------|------|------|
| 主控 | STM32F407VET6 | 1 | 系统控制 |
| 电机 | MG513XP28 | 2 | 驱动轮 |
| 传感器 | 灰度传感器 | 7 | 循迹检测 |
| 编码器 | 13脉冲/转 | 2 | 速度反馈 |
| 通信 | UART1 DMA | 1 | 数据传输 |

### 软件特性
- **任务调度**: 20ms车辆控制，10ms按键，150ms串口，30ms传感器
- **循迹精度**: 7传感器加权算法，精确线路跟踪
- **速度控制**: 编码器反馈，实时RPM计算
- **调试工具**: 纸飞机实时曲线，串口数据监控

## 🚀 快速开始

### 1. 硬件连接
```
电机连接:
- 左电机: TIM3_CH2(PA7) + Bin1(PC5) + Bin2(PC4)
- 右电机: TIM3_CH1(PA6) + Ain1(PA4) + Ain2(PA5)

传感器连接:
- 灰度传感器: P_2(PC0) 到 P_8(PC6)
- 编码器: E2A(TIM1) + E2B(TIM2)

通信接口:
- UART1: PA9(TX) + PA10(RX)
- 声光控制: PF11
```

### 2. 软件配置
```c
// 编译环境: Keil MDK-ARM
// 目标芯片: STM32F407VET6
// 时钟配置: 168MHz系统时钟
// 调试接口: SWD
```

### 3. 运行模式
- **模式0**: 停止模式
- **模式1**: 直线行驶至黑线停止
- **模式2**: 完整循迹任务 (A→B→C→D→A)
- **模式3**: 预留模式

### 4. 操作方法
1. 按键0/1预选模式
2. 按键2确认执行
3. 观察串口输出
4. 使用纸飞机工具查看实时数据

## 📞 技术支持

### 调试工具
- **串口监控**: 波特率115200，实时状态显示
- **纸飞机工具**: TEXT协议，实时曲线绘制
- **LED指示**: 系统状态和模式指示

### 常见问题
1. **循迹偏移**: 检查传感器校准和权重算法
2. **速度异常**: 验证编码器连接和PWM配置
3. **通信问题**: 确认UART配置和DMA设置

---

**项目版本**: v2.0
**最后更新**: 2025-07-07
**维护团队**: STM32循迹小车开发团队
