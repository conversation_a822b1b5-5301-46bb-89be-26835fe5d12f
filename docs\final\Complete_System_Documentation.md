# 🚗 STM32F407循迹小车完整系统文档

## 📋 文档信息
- **项目名称**: STM32F407智能循迹小车系统
- **版本**: v2.0
- **创建日期**: 2025-07-07
- **维护团队**: STM32循迹小车开发团队
- **文档类型**: 完整系统技术文档

## 🎯 系统概述

### 项目简介
本项目是一个基于STM32F407微控制器的智能循迹小车系统，采用先进的任务调度架构和模块化设计，具备完整的循迹功能、多种运行模式和丰富的调试工具。

### 核心特性
- **🎯 智能循迹**: 7路灰度传感器，精确线路跟踪
- **🔄 多种模式**: 停止/直线/循环/预留四种运行模式
- **⚡ 任务调度**: 基于时间片的多任务调度系统
- **📊 实时监控**: DMA串口通信，纸飞机调试工具
- **🏗️ 模块化设计**: 基于motor_driver架构的底层驱动
- **🔧 智能控制**: 编码器反馈，实时速度调节

## 🏗️ 系统架构

### 整体架构图
```
STM32F407循迹小车系统
├── 硬件抽象层 (HAL)
│   ├── STM32F407VET6 主控芯片
│   ├── 时钟系统 (168MHz)
│   ├── GPIO配置
│   ├── 定时器配置 (TIM1/TIM2/TIM3)
│   └── UART DMA配置
├── 驱动层 (Drivers)
│   ├── 电机驱动 (car.c) - 基于motor_driver架构
│   ├── 传感器驱动 (Gray.c) - 7路灰度传感器
│   ├── 按键驱动 (key.c) - 3键控制系统
│   └── 通信驱动 (my_uart.c) - DMA串口
├── 应用层 (Application)
│   ├── 任务调度器 (scheduler.c) - 多任务管理
│   ├── 运动控制 (motion_control.h) - 高级运动接口
│   └── 模式管理 - 4种运行模式
└── 调试层 (Debug)
    ├── 串口监控 - 实时状态显示
    ├── 纸飞机工具 - 实时曲线绘制
    └── LED指示 - 状态和模式显示
```

### 硬件配置详情

#### 电机控制系统
```
左电机 (Motor B):
- PWM控制: TIM3_CH2 (PA7)
- 方向控制: Bin1(PC5), Bin2(PC4)
- 编码器: TIM1 (E2A)

右电机 (Motor A):
- PWM控制: TIM3_CH1 (PA6)
- 方向控制: Ain1(PA4), Ain2(PA5)
- 编码器: TIM2 (E2B)

电机规格:
- 型号: MG513XP28
- 减速比: 28:1
- 编码器: 13脉冲/转
- 工作电压: 12V
```

#### 传感器系统
```
灰度传感器阵列:
- P_1: (硬件故障，已忽略)
- P_2: PE5 (GPIO输入)
- P_3: PE6 (GPIO输入)
- P_4: PF1 (GPIO输入)
- P_5: PF3 (GPIO输入)
- P_6: PF5 (GPIO输入)
- P_7: PF7 (GPIO输入)
- P_8: PF9 (GPIO输入)

传感器特性:
- 检测类型: 数字输入 (0/1)
- 阈值: 硬件设定
- 响应时间: <1ms
- 检测距离: 2-10mm
```

#### 通信和控制
```
串口通信:
- UART1: PA9(TX), PA10(RX)
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
- DMA: 使能

按键控制:
- KEY0: PE2 (模式预选)
- KEY1: PE3 (模式预选)
- KEY2: PE4 (确认执行)

声光控制:
- LED指示: PF11 (系统状态)
- 蜂鸣器: PF11 (低电平触发)
```

## 🔧 软件架构

### 任务调度系统
```c
static task_t scheduler_task[] = {
    {car_task,20,0},        // 车辆控制任务 - 20ms周期
    {key_task,10,0},        // 按键处理任务 - 10ms周期
    {uart_task,150,0},      // 串口通信任务 - 150ms周期
    {gray_task,30,0},       // 灰度传感器任务 - 30ms周期
};
```

#### 任务详细说明

**1. car_task (20ms)**
- 编码器数据读取和处理
- RPM计算和速度反馈
- 电机状态监控
- 纸飞机调试数据输出

**2. key_task (10ms)**
- 按键状态检测
- 模式切换逻辑
- 按键防抖处理

**3. uart_task (150ms)**
- 串口接收数据处理
- 命令解析和执行
- 系统状态输出

**4. gray_task (30ms)**
- 灰度传感器数据采集
- 循迹算法执行
- 运动控制决策
- 声光控制

### 电机驱动架构 (基于motor_driver)

#### 数据结构设计
```c
// 电机状态枚举
typedef enum {
    MOTOR_STATE_STOP = 0,     // 停止
    MOTOR_STATE_FORWARD,      // 正转
    MOTOR_STATE_BACKWARD,     // 反转
    MOTOR_STATE_ERROR         // 错误
} MotorState_t;

// 电机硬件配置
typedef struct {
    TIM_HandleTypeDef* htim;        // PWM定时器
    uint32_t channel;               // PWM通道
    GPIO_TypeDef* dir1_port;        // 方向控制1
    uint16_t dir1_pin;
    GPIO_TypeDef* dir2_port;        // 方向控制2
    uint16_t dir2_pin;
} MotorHW_t;

// 电机实体
typedef struct {
    MotorHW_t hw;                   // 硬件配置
    int8_t speed;                   // 当前速度 (-100到+100)
    MotorState_t state;             // 当前状态
    uint8_t enable;                 // 使能标志
    uint8_t reverse;                // 安装方向
} Motor_t;

// 小车控制实体
typedef struct {
    Motor_t left_motor;             // 左电机
    Motor_t right_motor;            // 右电机
    uint8_t initialized;            // 初始化标志
} Car_t;
```

#### 核心控制函数
```c
// 底层电机控制
int8_t Motor_Create(Motor_t* motor, ...);      // 创建电机实体
int8_t Motor_SetSpeed(Motor_t* motor, int8_t speed);  // 设置速度
int8_t Motor_Stop(Motor_t* motor);             // 停止电机
MotorState_t Motor_GetState(Motor_t* motor);   // 获取状态

// 高级运动控制
void move_forward(int8_t speed);               // 前进
void move_backward(int8_t speed);              // 后退
void turn_left(int8_t speed);                  // 左转
void turn_right(int8_t speed);                 // 右转
void stop_motors(void);                        // 停止
void move_custom(int8_t left_speed, int8_t right_speed);  // 自定义
```

## 🎮 运行模式系统

### 模式定义
```c
// 模式0: 停止模式
// - 所有电机停止
// - 系统待机状态

// 模式1: 直线检测模式
// - 直线行驶直到检测到黑线
// - 检测到黑线后立即停止
// - 保持停止状态直到切换模式

// 模式2: 完整循迹模式 (默认)
// - A→B: 断续线段 (直线行驶)
// - B→C: 黑线循迹
// - C→D: 断续线段 (直线行驶)
// - D→A: 黑线循迹
// - 完成后停止

// 模式3: 预留模式
// - 预留给未来功能扩展
```

### 循迹算法
```c
// 7传感器加权算法
int16_t calculate_line_position() {
    int32_t weighted_sum = 0;
    int32_t total_weight = 0;
    
    // 权重分配: -3, -2, -1, 0, 1, 2, 3
    int8_t weights[] = {-3, -2, -1, 0, 1, 2, 3};
    
    for(int i = 0; i < 7; i++) {
        if(sensor_values[i] > threshold) {
            weighted_sum += weights[i] * sensor_values[i];
            total_weight += sensor_values[i];
        }
    }
    
    return (total_weight > 0) ? (weighted_sum / total_weight) : 0;
}
```

### 运动控制策略
```c
// 基于位置偏差的速度控制
void line_following_control(int16_t position) {
    int8_t base_speed = 35;  // 基础速度
    int8_t correction = position * 2;  // 修正量
    
    int8_t left_speed = base_speed - correction;
    int8_t right_speed = base_speed + correction;
    
    // 速度限制
    left_speed = constrain(left_speed, 0, 60);
    right_speed = constrain(right_speed, 0, 60);
    
    move_custom(left_speed, right_speed);
}
```

## 📊 调试和监控系统

### 串口数据输出格式
```
[时间戳] [传感器状态] [电机状态] [模式信息] [特殊事件]

示例输出:
[12345] P2:0 P3:1 P4:1 P5:0 P6:0 P7:0 P8:0 | L:45F R:45F | MODE2_TRACKING [BEEP]
```

### 纸飞机调试工具
```c
// 速度曲线绘制
PRINT(plotter, "%.2f,%.2f", fabs(rpm_l), fabs(rpm_r));    // RPM曲线
PRINT(speed, "%.2f,%.2f", fabs(speed_l), fabs(speed_r));  // 线速度曲线

// 使用方法:
// 1. 打开纸飞机调试助手
// 2. 选择TEXT协议
// 3. 连接串口 (115200波特率)
// 4. 实时查看速度曲线
```

---

**文档版本**: v2.0  
**最后更新**: 2025-07-07  
**技术支持**: STM32循迹小车开发团队
