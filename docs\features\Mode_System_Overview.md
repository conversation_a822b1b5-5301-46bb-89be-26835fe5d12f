# 🎮 模式系统总览

## 📊 模式定义

### **模式列表**
```
模式0: 停止模式 - 完全静止，安全状态
模式1: 直线遇线永久停止 - 直行至黑线永久停止
模式2: (预留) - 待后续定义
模式3: (预留) - 待后续定义
默认: 循迹模式 - 7路传感器循迹控制
```

### **模式切换**
```c
// 按键控制
KEY0: sys_mode = (sys_mode+1)%4;  // 模式+1，循环0-3
KEY1: sys_mode = (sys_mode+2)%4;  // 模式+2，快速切换
KEY2: sys_ture = 1;               // 启动确认
```

## 🛑 模式0：停止模式

### **功能描述**
- **完全停止**: 电机完全停止运行
- **安全模式**: 系统安全状态
- **调试模式**: 便于系统检查

### **控制逻辑**
```c
case 0:  // 模式0：停止模式
    stop_motors();
    my_printf(&huart1," STOP");
    break;
```

### **串口输出**
```
[TRACK] X1111111 Mode:0 STOP
[TRACK] X1111111 Mode:0 STOP
```

### **应用场景**
- ✅ **系统启动**: 安全的初始状态
- ✅ **调试模式**: 检查系统状态
- ✅ **紧急停止**: 紧急情况下的安全模式
- ✅ **任务完成**: 任务结束后的停止状态

## 🚗 模式1：直线遇线永久停止

### **功能描述**
- **直线行驶**: 以40%速度直线前进
- **黑线检测**: 7路传感器实时监测
- **永久停止**: 检测到黑线后永久停止
- **状态锁定**: 停止后不再运行，直到切换模式

### **控制逻辑**
```c
case 1:  // 模式1：直线行驶，遇黑线后永久停止
{
    static uint8_t permanently_stopped = 0;

    if(permanently_stopped) {
        stop_motors();
        my_printf(&huart1," PERMANENTLY_STOPPED");
    } else if(sensor_sum > 0) {
        stop_motors();
        permanently_stopped = 1;
        my_printf(&huart1," STOP_LINE_DETECTED_PERMANENT");
    } else {
        move_forward(40);
        my_printf(&huart1," STRAIGHT");
    }
}
break;
```

### **串口输出**
```
[TRACK] X1111111 Mode:1 STRAIGHT
[TRACK] X0111111 Mode:1 [BEEP] STOP_LINE_DETECTED_PERMANENT
[TRACK] X0111111 Mode:1 PERMANENTLY_STOPPED
```

### **应用场景**
- ✅ **A→B任务**: 竞赛中的直线任务
- ✅ **起始阶段**: 从起点到第一条线
- ✅ **定点停车**: 精确停在黑线位置
- ✅ **距离测量**: 测量直线距离

## 🔄 模式2：预留

### **状态**
```c
case 2:  // 模式2：(预留)
    my_printf(&huart1," MODE_RESERVED");
    stop_motors();
    break;
```

### **串口输出**
```
[TRACK] X1111111 Mode:2 MODE_RESERVED
```

### **预留用途**
- 🔮 **循迹模式**: 可能用于特定的循迹功能
- 🔮 **复杂路径**: 可能用于复杂路径规划
- 🔮 **特殊任务**: 可能用于特定竞赛任务

## 🔄 模式3：预留

### **状态**
```c
case 3:  // 模式3：(预留)
    my_printf(&huart1," MODE_RESERVED");
    stop_motors();
    break;
```

### **串口输出**
```
[TRACK] X1111111 Mode:3 MODE_RESERVED
```

### **预留用途**
- 🔮 **高级功能**: 可能用于高级控制功能
- 🔮 **自定义模式**: 可能用于用户自定义功能
- 🔮 **扩展功能**: 可能用于系统功能扩展

## 🎯 默认模式：循迹控制

### **功能描述**
- **7路传感器**: 使用P_2到P_8进行循迹
- **阈值控制**: 简单的三状态控制
- **实时响应**: 30ms控制周期
- **连续运行**: 持续循迹直到手动停止

### **控制逻辑**
```c
default:  // 默认循迹模式
    if(sensor_sum > 0) {
        line_position = line_position / sensor_sum;
        
        if(line_position < 3000) {
            move_custom(20, 50);  // 右转
            my_printf(&huart1," RIGHT");
        } else if(line_position > 5000) {
            move_custom(50, 20);  // 左转
            my_printf(&huart1," LEFT");
        } else {
            move_forward(35);     // 直行
            my_printf(&huart1," STRAIGHT");
        }
    } else {
        move_forward(35);
        my_printf(&huart1," NO_LINE");
    }
    break;
```

### **串口输出**
```
[TRACK] X1100111 Mode:4 Pos:4000 STRAIGHT
[TRACK] X0111111 Mode:4 Pos:2000 RIGHT
[TRACK] X1111100 Mode:4 Pos:6000 LEFT
```

### **应用场景**
- ✅ **标准循迹**: 跟随黑线行驶
- ✅ **复杂路径**: 处理弯道和复杂路径
- ✅ **连续运行**: 长时间循迹运行
- ✅ **调试测试**: 测试循迹算法

## 🎮 模式操作指南

### **模式切换方法**
```
方法1: 顺序切换 (KEY0)
0 → 1 → 2 → 3 → 0 → ...

方法2: 跳跃切换 (KEY1)
0 → 2 → 0 → 2 → ...
1 → 3 → 1 → 3 → ...

方法3: 启动确认 (KEY2)
设置 sys_ture = 1
```

### **推荐操作流程**
```
1. 上电 → 默认模式0 (停止)
2. 按KEY0 → 切换到模式1 (直线)
3. 放置小车 → 在起始位置
4. 观察运行 → 直行至黑线停止
5. 按KEY0 → 切换到其他模式或停止
```

## 📊 模式对比表

| 模式 | 名称 | 功能 | 速度 | 检测 | 停止条件 |
|------|------|------|------|------|----------|
| 0 | 停止模式 | 完全停止 | 0% | 无 | 立即停止 |
| 1 | 直线遇线停止 | 直行检测 | 40% | 7路 | 检测到黑线 |
| 2 | 预留 | 待定义 | - | - | 待定义 |
| 3 | 预留 | 待定义 | - | - | 待定义 |
| 默认 | 循迹模式 | 跟线行驶 | 35% | 7路 | 手动停止 |

## 🔧 系统配置

### **全局变量**
```c
extern uint8_t sys_mode;  // 当前模式 (0-3)
extern uint8_t sys_ture;  // 启动标志
```

### **任务调度**
```c
static task_t scheduler_task[] =
{
    {car_task,20,0},
    {key_task,10,0},      // 按键检测，10ms
    {uart_task,150,0},
    {gray_task,30,0},     // 模式控制，30ms
};
```

### **串口输出格式**
```
[TRACK] X1111111 Mode:X STATUS
        ↑        ↑      ↑
    传感器状态   模式号  运行状态
```

## 📈 扩展建议

### **模式2可能实现**
- **高速循迹**: 提高循迹速度
- **精确循迹**: 提高循迹精度
- **复杂路径**: 处理特殊路径

### **模式3可能实现**
- **自定义路径**: 用户定义的路径
- **多任务模式**: 组合多个基础任务
- **智能模式**: 自适应的智能控制

### **系统优化**
- **模式记忆**: 记住上次使用的模式
- **参数调整**: 每个模式独立的参数
- **状态保存**: 模式切换时保存状态

## 📁 相关文档

### **模式文档**
- `Mode0_Stop_Mode.md` - 模式0详细说明
- `Mode1_Straight_Until_Line.md` - 模式1详细说明
- `Simple_Line_Following_Control.md` - 默认循迹模式

### **系统文档**
- `7_Sensor_Configuration.md` - 传感器配置
- `Sound_Light_Control_Implementation.md` - 声光控制
- `Complete_Tracking_Car_System.md` - 完整系统说明

---

**系统版本**: v1.0  
**模式数量**: 4个模式 + 1个默认模式  
**实现状态**: 模式0、1已实现，模式2、3预留  
**更新时间**: 2025-06-20
