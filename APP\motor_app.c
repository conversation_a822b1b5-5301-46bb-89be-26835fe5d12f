#include "mydefine.h"
#include "motor_driver.h"

MOTOR left_motor;
MOTOR right_motor;

void Motor_App_Init(void)
{
    Motor_Config_Init(&left_motor, &htim3, TIM_CHANNEL_1, Ain1_GPIO_Port, Ain1_Pin, Ain2_GPIO_Port, Ain2_Pin, 0, 30);
    Motor_Config_Init(&right_motor, &htim3, TIM_CHANNEL_2, Bin1_GPIO_Port, Bin1_Pin, Bin2_GPIO_Port, Bin2_Pin, 0, 30);
}

void Motor_App_Stop(void)
{
    Motor_Stop(&left_motor);
    Motor_Stop(&right_motor);
}

void Motor_App_Forward(int8_t speed)
{
    // 限制最大速度，避免过于激进
    if(speed > 40) speed = 40;

    Motor_Set_Speed(&left_motor, -speed);
    Motor_Set_Speed(&right_motor, -speed);
}

void Motor_App_Backward(int8_t speed)
{
    Motor_Set_Speed(&left_motor, speed);    
    Motor_Set_Speed(&right_motor, speed);   
}

void Motor_App_Turn_Left(int8_t speed)
{
    Motor_Set_Speed(&left_motor, speed);    
    Motor_Set_Speed(&right_motor, -speed);  
}

void Motor_App_Turn_Right(int8_t speed)
{
    Motor_Set_Speed(&left_motor, -speed);  
    Motor_Set_Speed(&right_motor, speed);   
}

void Motor_App_Set_Speed(int8_t left_speed, int8_t right_speed)
{
    // 限制速度范围，避免过于激进
    if(left_speed > 50) left_speed = 50;
    if(left_speed < -50) left_speed = -50;
    if(right_speed > 50) right_speed = 50;
    if(right_speed < -50) right_speed = -50;

    Motor_Set_Speed(&left_motor, -left_speed);
    Motor_Set_Speed(&right_motor, -right_speed);
}
