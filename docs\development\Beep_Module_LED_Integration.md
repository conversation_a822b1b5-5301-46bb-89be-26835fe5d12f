# 🔊💡 Beep模块自带LED集成说明

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: Alex (工程师)
- **状态**: 已确认
- **目的**: 说明beep模块自带LED的特性和控制方法

## 🎯 硬件特性

### Beep模块集成设计
- **一体化设计**: beep模块自带LED指示灯
- **单GPIO控制**: 一个引脚同时控制声音和光
- **同步效果**: 声音和LED同时开启/关闭
- **简化电路**: 无需额外的LED控制电路

### 硬件连接
```
STM32F407 PF11 ──► Beep模块 (自带LED)
                   ├── 蜂鸣器 🔊
                   └── LED指示灯 💡
```

## 🔧 控制逻辑

### GPIO控制方式
```c
// 触发声光 (beep模块自带LED)
HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_RESET); // 低电平触发
// 效果: 蜂鸣器响 + LED亮

// 关闭声光
HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_SET);   // 高电平关闭
// 效果: 蜂鸣器停 + LED灭
```

### 触发特性
- **触发电平**: 低电平触发 (GPIO_PIN_RESET)
- **关闭电平**: 高电平关闭 (GPIO_PIN_SET)
- **同步控制**: 声音和LED完全同步
- **即时响应**: 无延迟，即时开启/关闭

## 📊 实现代码

### 当前实现 (scheduler.c)
```c
// 非阻塞声光控制 - beep模块自带LED，一个GPIO控制声音+光
static uint32_t beep_start_time = 0;
static uint8_t beep_active = 0;

// 检测从无线到有线的状态变化 - 触发声光控制
if(!last_line_detected && current_line_detected && !beep_active) {
    // 开始声光控制 (beep模块自带LED)
    beep_start_time = HAL_GetTick();
    beep_active = 1;
    HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_RESET); // 触发beep+LED (低电平)
    my_printf(&huart1," [BEEP+LED]");  // 串口提示声光一体
}

// 检查是否需要关闭声光 (100ms后自动关闭)
if(beep_active && (HAL_GetTick() - beep_start_time >= 100)) {
    beep_active = 0;
    HAL_GPIO_WritePin(GPIOF, GPIO_PIN_11, GPIO_PIN_SET);   // 关闭beep+LED (高电平)
}
```

### 串口输出效果
```
P2:1 P3:1 P4:1 P5:1 P6:1 P7:1 P8:1 | ERR:0.00 ACT:0 | L:20F R:20F | MODE2
P2:1 P3:0 P4:0 P5:1 P6:1 P7:1 P8:1 | ERR:-2.50 ACT:2 | L:28F R:12F | MODE2 [BEEP+LED]
P2:1 P3:0 P4:0 P5:0 P6:1 P7:1 P8:1 | ERR:-2.00 ACT:3 | L:26F R:14F | MODE2
```

说明：
- `[BEEP+LED]` 表示声光一体触发
- 100ms后自动关闭，无需额外提示

## 🎯 技术优势

### 1. 硬件简化
- **减少引脚**: 只需一个GPIO引脚
- **简化电路**: 无需额外LED驱动电路
- **降低成本**: 集成设计降低硬件成本
- **提高可靠性**: 减少连接点，提高可靠性

### 2. 软件简化
- **统一控制**: 一个GPIO控制两种效果
- **同步保证**: 声音和光完全同步
- **代码简洁**: 无需分别控制声音和LED
- **维护简单**: 只需维护一个控制逻辑

### 3. 用户体验
- **视听结合**: 声音+光的双重提示
- **效果明显**: 更容易被注意到
- **状态清晰**: 明确的状态指示
- **调试友好**: 便于系统调试

## 🎮 应用场景

### 循迹状态提示
- **线路检测**: 检测到黑线时声光提示
- **状态变化**: 从白色区域进入黑线的瞬间
- **调试辅助**: 帮助调试传感器和算法
- **演示效果**: 增强演示的视听效果

### 系统状态指示
- **启动提示**: 系统启动时的状态指示
- **模式切换**: 模式切换时的确认提示
- **错误警告**: 系统错误时的警告提示
- **完成信号**: 任务完成时的成功提示

## ⚙️ 配置参数

### 时间参数
```c
// 声光持续时间 (可调节)
#define BEEP_DURATION_MS    100     // 当前100ms
#define BEEP_SHORT_MS       50      // 短声光
#define BEEP_LONG_MS        200     // 长声光
```

### 触发条件
```c
// 边沿检测触发
if(!last_line_detected && current_line_detected && !beep_active) {
    // 触发条件:
    // 1. 上次无线检测
    // 2. 当前有线检测  
    // 3. 声光未激活
}
```

### 控制逻辑
```c
// 状态机控制
typedef enum {
    BEEP_STATE_IDLE,        // 空闲状态
    BEEP_STATE_ACTIVE,      // 激活状态
    BEEP_STATE_COOLDOWN     // 冷却状态 (可选)
} BeepState_t;
```

## 🔧 扩展功能

### 1. 多种模式
```c
// 不同的声光模式
typedef enum {
    BEEP_MODE_SINGLE,       // 单次声光
    BEEP_MODE_DOUBLE,       // 双次声光
    BEEP_MODE_PULSE,        // 脉冲声光
    BEEP_MODE_LONG          // 长声光
} BeepMode_t;
```

### 2. 可配置参数
```c
// 声光配置结构体
typedef struct {
    uint16_t duration_ms;   // 持续时间
    uint8_t repeat_count;   // 重复次数
    uint16_t interval_ms;   // 间隔时间
    uint8_t enable;         // 使能开关
} BeepConfig_t;
```

### 3. 智能控制
```c
// 根据不同事件使用不同声光模式
void trigger_beep_by_event(BeepEvent_t event) {
    switch(event) {
        case EVENT_LINE_DETECTED:
            trigger_beep(BEEP_MODE_SINGLE, 100);
            break;
        case EVENT_MODE_CHANGED:
            trigger_beep(BEEP_MODE_DOUBLE, 50);
            break;
        case EVENT_ERROR:
            trigger_beep(BEEP_MODE_LONG, 500);
            break;
    }
}
```

## ⚠️ 注意事项

### 1. 电气特性
- 确认beep模块的工作电压
- 检查GPIO驱动能力是否足够
- 验证触发电平的正确性

### 2. 时序控制
- 避免过于频繁的触发
- 控制声光持续时间合理
- 防止声光重叠

### 3. 系统集成
- 确保不影响其他功能
- 验证与任务调度的兼容性
- 测试在不同模式下的表现

## 🔄 未来优化

### 1. 音调控制
如果beep模块支持音调控制，可以实现：
- 不同频率的提示音
- 音乐播放功能
- 更丰富的声音效果

### 2. LED亮度控制
如果支持PWM控制，可以实现：
- LED亮度调节
- 呼吸灯效果
- 渐变效果

### 3. 智能适应
- 根据环境噪音调节音量
- 根据环境光线调节LED亮度
- 自适应的提示策略

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**技术支持**: STM32循迹小车开发团队
