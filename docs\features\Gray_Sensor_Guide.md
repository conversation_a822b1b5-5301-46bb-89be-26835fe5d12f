# 📡 灰度传感器循迹指南

## 📊 功能概述

修正了灰度传感器相关的错误并实现了基础循迹功能：
- **8路灰度传感器**: 正确读取8个不同的GPIO引脚
- **循迹算法**: 基于加权平均的线位置检测
- **运动控制**: 根据线位置自动调整转向
- **任务调度**: 30ms周期的灰度传感器任务

## 🔧 修正的问题

### **1. 函数名错误**
```c
// 修正前 (拼写错误)
void gtay_task()
{gtay_task,30,0}

// 修正后 (正确拼写)
void gray_task()
{gray_task,30,0}
```

### **2. 引脚读取错误**
```c
// 修正前 (所有传感器读取同一引脚)
gray_buf[0] = HAL_GPIO_ReadPin(P_1_GPIO_Port,P_1_Pin);
gray_buf[1] = HAL_GPIO_ReadPin(P_1_GPIO_Port,P_1_Pin);  // 错误!
gray_buf[2] = HAL_GPIO_ReadPin(P_1_GPIO_Port,P_1_Pin);  // 错误!
...

// 修正后 (每个传感器读取对应引脚)
gray_buf[0] = HAL_GPIO_ReadPin(P_1_GPIO_Port, P_1_Pin);  // 传感器1
gray_buf[1] = HAL_GPIO_ReadPin(P_2_GPIO_Port, P_2_Pin);  // 传感器2
gray_buf[2] = HAL_GPIO_ReadPin(P_3_GPIO_Port, P_3_Pin);  // 传感器3
...
```

## 📈 灰度传感器配置

### **8路传感器布局**
```
[0] [1] [2] [3] [4] [5] [6] [7]
 ↑   ↑   ↑   ↑   ↑   ↑   ↑   ↑
P_1 P_2 P_3 P_4 P_5 P_6 P_7 P_8
左侧              中间              右侧
```

### **传感器读取**
```c
void Gray_get(uint8_t *gray_buf)
{
    // 8路灰度传感器读取 (从左到右)
    gray_buf[0] = HAL_GPIO_ReadPin(P_1_GPIO_Port, P_1_Pin);   // 最左侧
    gray_buf[1] = HAL_GPIO_ReadPin(P_2_GPIO_Port, P_2_Pin);   // 传感器2
    gray_buf[2] = HAL_GPIO_ReadPin(P_3_GPIO_Port, P_3_Pin);   // 传感器3
    gray_buf[3] = HAL_GPIO_ReadPin(P_4_GPIO_Port, P_4_Pin);   // 传感器4
    gray_buf[4] = HAL_GPIO_ReadPin(P_5_GPIO_Port, P_5_Pin);   // 传感器5
    gray_buf[5] = HAL_GPIO_ReadPin(P_6_GPIO_Port, P_6_Pin);   // 传感器6
    gray_buf[6] = HAL_GPIO_ReadPin(P_7_GPIO_Port, P_7_Pin);   // 传感器7
    gray_buf[7] = HAL_GPIO_ReadPin(P_8_GPIO_Port, P_8_Pin);   // 最右侧
}
```

## 🎯 循迹算法

### **线位置检测算法**
```c
// 计算线位置 (加权平均)
int line_position = 0;
int sensor_sum = 0;

for(int i = 0; i < 8; i++) {
    if(gray_buff[i] == 0) {  // 0表示检测到黑线
        line_position += i * 1000;  // 位置权重
        sensor_sum += 1000;
    }
}

if(sensor_sum > 0) {
    line_position = line_position / sensor_sum;  // 加权平均位置
}
```

### **位置权重说明**
```
传感器位置:  [0] [1] [2] [3] [4] [5] [6] [7]
权重值:      0   1   2   3   4   5   6   7
权重×1000:   0  1000 2000 3000 4000 5000 6000 7000

线位置范围:
- 0-2000:    线在左侧
- 2000-5000: 线在中间
- 5000-7000: 线在右侧
```

## 🚗 运动控制策略

### **转向控制逻辑**
```c
if(line_position < 3000) {
    // 线在左侧，需要左转
    move_custom(20, 50);  // 左轮慢，右轮快
} else if(line_position > 4000) {
    // 线在右侧，需要右转
    move_custom(50, 20);  // 左轮快，右轮慢
} else {
    // 线在中间，直行
    move_forward(40);     // 双轮同速
}
```

### **异常处理**
```c
if(sensor_sum == 0) {
    // 没有检测到线，停止或搜索
    stop_motors();
    // 或者执行搜索算法
}
```

## 📊 传感器数据解读

### **数据格式**
```c
uint8_t gray_buff[8];  // 8个传感器的数据

// 数据含义
0: 检测到黑线 (或深色)
1: 检测到白线 (或浅色)
```

### **典型数据模式**
```c
// 线在左侧
gray_buff = {0, 0, 1, 1, 1, 1, 1, 1};

// 线在中间
gray_buff = {1, 1, 1, 0, 0, 1, 1, 1};

// 线在右侧  
gray_buff = {1, 1, 1, 1, 1, 1, 0, 0};

// 宽线
gray_buff = {1, 0, 0, 0, 0, 0, 0, 1};

// 无线
gray_buff = {1, 1, 1, 1, 1, 1, 1, 1};
```

## 🔧 使用方法

### **启用循迹功能**
```c
// 在gray_task中取消注释循迹算法
void gray_task()
{
    Gray_get(gray_buff);
    
    // 取消注释以下代码启用循迹
    /*
    // 循迹算法代码...
    */
}
```

### **与PID控制的关系**
- **当前状态**: 循迹算法被注释，避免与PID速度控制冲突
- **选择使用**: 可以选择使用PID速度控制或循迹控制
- **混合控制**: 高级用法可以结合两者

## ⚙️ 参数调整

### **转向灵敏度**
```c
// 调整转向阈值
if(line_position < 2500) {      // 更敏感的左转
if(line_position > 4500) {      // 更敏感的右转

// 调整转向速度
move_custom(10, 60);  // 更大的速度差，转向更急
move_custom(30, 40);  // 较小的速度差，转向更缓
```

### **基础速度**
```c
move_forward(30);     // 较慢的直行速度
move_forward(60);     // 较快的直行速度
```

### **任务频率**
```c
{gray_task, 20, 0},   // 20ms，更高频率
{gray_task, 50, 0},   // 50ms，较低频率
```

## 📈 性能优化

### **滤波处理**
```c
// 可以添加传感器数据滤波
static uint8_t last_gray_buff[8];
for(int i = 0; i < 8; i++) {
    // 简单滤波
    gray_buff[i] = (gray_buff[i] + last_gray_buff[i]) / 2;
    last_gray_buff[i] = gray_buff[i];
}
```

### **PID循迹控制**
```c
// 高级应用：将线位置作为PID输入
float line_error = line_position - 3500;  // 3500为中心位置
float pid_output = PID_Calculate(&line_pid, line_error);
// 根据PID输出调整转向
```

## ⚠️ 注意事项

### **硬件要求**
- 确保8个灰度传感器正确连接到P_1到P_8引脚
- 传感器高度和角度要一致
- 光照条件要适中

### **软件注意**
- 循迹算法与PID速度控制可能冲突
- 根据实际需要选择使用哪种控制方式
- 调试时可以通过串口输出传感器数据

### **调试建议**
```c
// 在gray_task中添加调试输出
my_printf(&huart1, "Gray: %d%d%d%d%d%d%d%d Pos:%d\r\n",
          gray_buff[0], gray_buff[1], gray_buff[2], gray_buff[3],
          gray_buff[4], gray_buff[5], gray_buff[6], gray_buff[7],
          line_position);
```

---

**修正完成时间**: 2025-06-20  
**修正内容**: 函数名拼写 + 引脚读取 + 循迹算法  
**状态**: ✅ 已修正
