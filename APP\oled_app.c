#include "oled_app.h"
#include "oled/oled.h"
#include "mydefine.h"
#include "Gray.h"
#include "mpu6050_app.h"
#include <stdio.h>
#include <stdarg.h>

// 全局变量已在mydefine.h中声明

/**
 * @brief	使用类似printf的方式显示字符串，显示6x8的小号ASCII字符
 * @param x  Character position on the X-axis  range：0 - 127
 * @param y  Character position on the Y-axis  range：0 - 3
 * 例如：oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后字符串
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

// OLED页面管理
static uint8_t current_page = 0;
static uint8_t total_pages = 4;

void oled_task(void)
{
    // 清屏
    OLED_Clear();

    // 根据当前页面显示不同内容
    switch(current_page) {
        case 0:
            OLED_Display_System_Status();
            break;
        case 1:
            OLED_Display_Sensor_Data();
            break;
        case 2:
            OLED_Display_Motor_Status();
            break;
        case 3:
            OLED_Display_MPU6050_Data();
            break;
        default:
            current_page = 0;
            break;
    }

    // 显示页面指示器
    oled_printf(110, 0, "%d/%d", current_page + 1, total_pages);
}

// OLED初始化
void OLED_App_Init(void)
{
    OLED_Init();
    OLED_Clear();
    oled_printf(0, 0, "Tracking Car");
    oled_printf(0, 1, "Initializing...");
    HAL_Delay(1000);
}

// 显示系统状态
void OLED_Display_System_Status(void)
{
    oled_printf(0, 0, "System Status");
    oled_printf(0, 1, "Mode:%d Pre:%d", sys_mode, pre_mode);

    // 显示当前状态
    const char* mode_names[] = {"STOP", "LINE", "TRACK", "RES3"};
    if(sys_mode < 4) {
        oled_printf(0, 2, "State:%s", mode_names[sys_mode]);
    }
    oled_printf(0, 3, "Time:%ld", HAL_GetTick());
}

// 显示传感器数据
void OLED_Display_Sensor_Data(void)
{
    float position_error = Gray_Get_Position_Error();
    uint8_t active_sensors = Gray_Get_Active_Sensor_Count();

    oled_printf(0, 0, "Sensor Data");
    oled_printf(0, 1, "%d%d%d%d%d%d%d",
                gray_buff[1], gray_buff[2], gray_buff[3], gray_buff[4],
                gray_buff[5], gray_buff[6], gray_buff[7]);
    oled_printf(0, 2, "Err:%.2f", position_error);
    oled_printf(0, 3, "Active:%d", active_sensors);
}

// 显示电机状态
void OLED_Display_Motor_Status(void)
{
    oled_printf(0, 0, "Motor Status");
    oled_printf(0, 1, "Left: OK");
    oled_printf(0, 2, "Right: OK");
    oled_printf(0, 3, "PWM: TIM3");
}

// 显示MPU6050数据
void OLED_Display_MPU6050_Data(void)
{
    float yaw_error = Get_Yaw_Error();
    int8_t direction_correction = Get_Direction_Correction();

    oled_printf(0, 0, "MPU6050 Data");
    oled_printf(0, 1, "Y:%.1f R:%.1f", Yaw, Roll);
    oled_printf(0, 2, "P:%.1f", Pitch);
    oled_printf(0, 3, "YErr:%.1f Dir:%d", yaw_error, direction_correction);
}

// 页面管理函数
void OLED_Next_Page(void)
{
    current_page = (current_page + 1) % total_pages;
}

void OLED_Prev_Page(void)
{
    current_page = (current_page + total_pages - 1) % total_pages;
}

void OLED_Set_Page(uint8_t page)
{
    if(page < total_pages) {
        current_page = page;
    }
}
