#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART1_RX
Dma.Request1=USART1_TX
Dma.RequestsNb=2
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_TX.1.Instance=DMA2_Stream7
Dma.USART1_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.1.Mode=DMA_NORMAL
Dma.USART1_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407ZGT6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=SYS
Mcu.IP4=TIM1
Mcu.IP5=TIM2
Mcu.IP6=TIM3
Mcu.IP7=TIM14
Mcu.IP8=USART1
Mcu.IPNb=9
Mcu.Name=STM32F407Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PF5
Mcu.Pin11=PF7
Mcu.Pin12=PF9
Mcu.Pin13=PH0-OSC_IN
Mcu.Pin14=PH1-OSC_OUT
Mcu.Pin15=PA0-WKUP
Mcu.Pin16=PA1
Mcu.Pin17=PA4
Mcu.Pin18=PA5
Mcu.Pin19=PA6
Mcu.Pin2=PE4
Mcu.Pin20=PA7
Mcu.Pin21=PC4
Mcu.Pin22=PC5
Mcu.Pin23=PF11
Mcu.Pin24=PE9
Mcu.Pin25=PE11
Mcu.Pin26=PE14
Mcu.Pin27=PE15
Mcu.Pin28=PA9
Mcu.Pin29=PA10
Mcu.Pin3=PE5
Mcu.Pin30=PA13
Mcu.Pin31=PA14
Mcu.Pin32=PB6
Mcu.Pin33=PB7
Mcu.Pin34=VP_SYS_VS_Systick
Mcu.Pin35=VP_TIM14_VS_ClockSourceINT
Mcu.Pin4=PE6
Mcu.Pin5=PC14-OSC32_IN
Mcu.Pin6=PC15-OSC32_OUT
Mcu.Pin7=PF0
Mcu.Pin8=PF1
Mcu.Pin9=PF3
Mcu.PinsNb=36
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407ZGTx
MxCube.Version=6.14.0
MxDb.Version=DB.6.0.140
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream7_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_Label
PA0-WKUP.GPIO_Label=E1A
PA0-WKUP.Signal=S_TIM2_CH1_ETR
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=E1B
PA1.Signal=S_TIM2_CH2
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=Ain1
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=Ain2
PA5.Locked=true
PA5.Signal=GPIO_Output
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=PWMA
PA6.Locked=true
PA6.Signal=S_TIM3_CH1
PA7.GPIOParameters=GPIO_Label
PA7.GPIO_Label=PWMB
PA7.Signal=S_TIM3_CH2
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB6.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PB6.GPIO_Label=scl
PB6.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB6.GPIO_PuPd=GPIO_NOPULL
PB6.Locked=true
PB6.PinState=GPIO_PIN_SET
PB6.Signal=GPIO_Output
PB7.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PB7.GPIO_Label=sda
PB7.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB7.GPIO_PuPd=GPIO_NOPULL
PB7.Locked=true
PB7.PinState=GPIO_PIN_SET
PB7.Signal=GPIO_Output
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC4.GPIOParameters=GPIO_Label
PC4.GPIO_Label=Bin2
PC4.Locked=true
PC4.Signal=GPIO_Output
PC5.GPIOParameters=GPIO_Label
PC5.GPIO_Label=Bin1
PC5.Locked=true
PC5.Signal=GPIO_Output
PE11.GPIOParameters=GPIO_Label
PE11.GPIO_Label=E2B
PE11.Signal=S_TIM1_CH2
PE14.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PE14.GPIO_Label=SCL
PE14.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PE14.GPIO_PuPd=GPIO_NOPULL
PE14.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE14.Locked=true
PE14.PinState=GPIO_PIN_SET
PE14.Signal=GPIO_Output
PE15.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PE15.GPIO_Label=SDA
PE15.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PE15.GPIO_PuPd=GPIO_NOPULL
PE15.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE15.Locked=true
PE15.PinState=GPIO_PIN_SET
PE15.Signal=GPIO_Output
PE2.GPIOParameters=GPIO_PuPd,GPIO_Label
PE2.GPIO_Label=key0
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_PuPd,GPIO_Label
PE3.GPIO_Label=key1
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_PuPd,GPIO_Label
PE4.GPIO_Label=key2
PE4.GPIO_PuPd=GPIO_PULLUP
PE4.Locked=true
PE4.Signal=GPIO_Input
PE5.GPIOParameters=GPIO_PuPd,GPIO_Label
PE5.GPIO_Label=P_2
PE5.GPIO_PuPd=GPIO_PULLUP
PE5.Locked=true
PE5.Signal=GPIO_Input
PE6.GPIOParameters=GPIO_PuPd,GPIO_Label
PE6.GPIO_Label=P_3
PE6.GPIO_PuPd=GPIO_PULLUP
PE6.Locked=true
PE6.Signal=GPIO_Input
PE9.GPIOParameters=GPIO_Label
PE9.GPIO_Label=E2A
PE9.Signal=S_TIM1_CH1
PF0.GPIOParameters=GPIO_PuPd,GPIO_Label
PF0.GPIO_Label=P_1
PF0.GPIO_PuPd=GPIO_PULLUP
PF0.Locked=true
PF0.Signal=GPIO_Input
PF1.GPIOParameters=GPIO_PuPd,GPIO_Label
PF1.GPIO_Label=P_4
PF1.GPIO_PuPd=GPIO_PULLUP
PF1.Locked=true
PF1.Signal=GPIO_Input
PF11.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PF11.GPIO_Label=FMD_LED
PF11.GPIO_PuPd=GPIO_PULLUP
PF11.Locked=true
PF11.PinState=GPIO_PIN_RESET
PF11.Signal=GPIO_Output
PF3.GPIOParameters=GPIO_PuPd,GPIO_Label
PF3.GPIO_Label=P_5
PF3.GPIO_PuPd=GPIO_PULLUP
PF3.Locked=true
PF3.Signal=GPIO_Input
PF5.GPIOParameters=GPIO_PuPd,GPIO_Label
PF5.GPIO_Label=P_6
PF5.GPIO_PuPd=GPIO_PULLUP
PF5.Locked=true
PF5.Signal=GPIO_Input
PF7.GPIOParameters=GPIO_PuPd,GPIO_Label
PF7.GPIO_Label=P_7
PF7.GPIO_PuPd=GPIO_PULLUP
PF7.Locked=true
PF7.Signal=GPIO_Input
PF9.GPIOParameters=GPIO_PuPd,GPIO_Label
PF9.GPIO_Label=P_8
PF9.GPIO_PuPd=GPIO_PULLUP
PF9.Locked=true
PF9.Signal=GPIO_Input
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=tracking_car.ioc
ProjectManager.ProjectName=tracking_car
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_TIM14_Init-TIM14-false-HAL-true,5-MX_TIM3_Init-TIM3-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM2_Init-TIM2-false-HAL-true,8-MX_USART1_UART_Init-USART1-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.S_TIM1_CH1.0=TIM1_CH1,Encoder_Interface
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,Encoder_Interface
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM2_CH1_ETR.0=TIM2_CH1,Encoder_Interface
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,Encoder_Interface
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,PWM Generation1 CH1
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,PWM Generation2 CH2
SH.S_TIM3_CH2.ConfNb=1
TIM1.IC1Filter=15
TIM1.IC2Filter=15
TIM1.IC2Polarity=TIM_ICPOLARITY_FALLING
TIM1.IPParameters=IC1Filter,IC2Filter,Prescaler,IC2Polarity
TIM1.Prescaler=2-1
TIM14.IPParameters=Prescaler,Period
TIM14.Period=100-1
TIM14.Prescaler=168-1
TIM2.IC1Filter=15
TIM2.IC2Filter=15
TIM2.IC2Polarity=TIM_ICPOLARITY_FALLING
TIM2.IPParameters=Period,IC1Filter,IC2Filter,IC2Polarity,Prescaler
TIM2.Period=65535
TIM2.Prescaler=2-1
TIM3.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM3.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM3.IPParameters=Channel-PWM Generation1 CH1,Period,Prescaler,Pulse-PWM Generation1 CH1,Channel-PWM Generation2 CH2
TIM3.Period=100-1
TIM3.Prescaler=168-1
TIM3.Pulse-PWM\ Generation1\ CH1=50
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM14_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM14_VS_ClockSourceINT.Signal=TIM14_VS_ClockSourceINT
board=custom
