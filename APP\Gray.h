#ifndef GRAY_H
#define GRAY_H

#include "mydefine.h"

/* 灰度传感器配置 - 基于2024_H_Car蓝本 */
#define GRAY_SENSOR_COUNT 8         // 8路灰度传感器
#define GRAY_SENSOR_VALID_COUNT 7   // 有效传感器数量 (忽略P_1)

/* 灰度传感器权重配置 - 基于蓝本优化 */
extern float gray_weights[GRAY_SENSOR_COUNT];

/* 全局变量 */
extern uint8_t gray_digital_data;      // 数字传感器数据
extern float g_line_position_error;    // 循迹误差值
extern uint8_t gray_sensor_status[GRAY_SENSOR_COUNT];  // 各传感器状态

/* 函数声明 - 基于2024_H_Car蓝本 */
void Gray_Init(void);                   // 初始化
void Gray_Task(void);                   // 巡线任务 (基于蓝本算法)
void Gray_get(uint8_t *gray_buf);       // 读取传感器数据 (兼容原接口)
float Gray_Get_Position_Error(void);    // 获取位置误差
uint8_t Gray_Get_Active_Sensor_Count(void); // 获取激活传感器数量
// 方向控制相关函数已移除

#endif
