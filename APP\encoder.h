#ifndef ENCODER_H
#define ENCODER_H

#include "main.h"
#include "tim.h"

// 编码器参数配置
#define ENCODER_PPR 1040        // 编码器每转脉冲数 (13线*20减速比*4倍频)
#define WHEEL_DIAMETER_CM 6.5f  // 车轮直径(cm)
#define PI 3.14159265f
#define WHEEL_CIRCUMFERENCE_CM (WHEEL_DIAMETER_CM * PI)  // 车轮周长
#define SAMPLING_TIME_S 0.02f   // 采样时间20ms

// 编码器数据结构
typedef struct {
    TIM_HandleTypeDef *htim;    // 定时器句柄
    uint8_t reverse;            // 是否反向 (0-正常, 1-反向)
    int16_t count;              // 当前周期计数
    int32_t total_count;        // 累计总计数
    float speed_cm_s;           // 速度 cm/s
    float speed_rpm;            // 转速 RPM
} Encoder_t;

// 编码器函数
void encoder_init(void);
void encoder_update(void);
float encoder_get_left_speed(void);
float encoder_get_right_speed(void);
float encoder_get_left_rpm(void);
float encoder_get_right_rpm(void);

#endif
