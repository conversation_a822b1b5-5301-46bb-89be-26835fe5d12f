#ifndef __MPU6050_APP_H__
#define __MPU6050_APP_H__

#include "MyDefine.h"

// MPU6050初始化和任务函数
void Mpu6050_Init(void);
void Mpu6050_Task(void);

// 姿态角数据 (全局变量)
extern float Pitch, Roll, Yaw;

// 方向控制相关函数
void Direction_Control_Init(void);
void Direction_Control_Update(void);
float Get_Yaw_Error(void);
int8_t Get_Direction_Correction(void);
void Set_Target_Direction(float target_yaw);
void Reset_Target_Direction(void);

// 内部辅助函数
float convert_to_continuous_yaw(float current_yaw);

#endif
