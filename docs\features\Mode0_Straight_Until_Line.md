# 🛑 模式0：停止模式

## 📊 功能概述

模式0是安全停止模式，确保小车完全静止：
- **完全停止**: 小车电机完全停止运行
- **安全模式**: 作为系统的安全状态
- **调试模式**: 便于调试和检查系统状态
- **默认状态**: 可作为系统启动的默认安全状态

## 🎯 工作原理

### **控制逻辑**
```c
case 0:  // 模式0：停止模式
    stop_motors();
    my_printf(&huart1," STOP");
    break;
```

### **停止机制**
```c
// 无条件停止
stop_motors();  // 调用停止函数

// stop_motors()函数实现 (在car.c中)
void stop_motors(void)
{
    // 左电机停止
    HAL_GPIO_WritePin(Ain1_GPIO_Port, Ain1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Ain2_GPIO_Port, Ain2_Pin, GPIO_PIN_RESET);
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, 0);

    // 右电机停止
    HAL_GPIO_WritePin(Bin1_GPIO_Port, Bin1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Bin2_GPIO_Port, Bin2_Pin, GPIO_PIN_RESET);
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, 0);
}
```

## ⚙️ 技术参数

### **运行参数**
```
模式编号: 0
直行速度: 40% (可调)
检测范围: P_2到P_8 (7路传感器)
停止条件: 任意传感器检测到黑线
声光提示: 检测到黑线时触发
```

### **传感器配置**
```
有效传感器: P_2, P_3, P_4, P_5, P_6, P_7, P_8
忽略传感器: P_1 (硬件问题)
检测逻辑: 0=黑线, 1=白线
触发条件: 任意传感器为0
```

## 📊 串口输出

### **正常运行状态**
```
[TRACK] X1111111 Mode:0 STRAIGHT
[TRACK] X1111111 Mode:0 STRAIGHT
[TRACK] X1111111 Mode:0 STRAIGHT
```

### **检测到黑线状态**
```
[TRACK] X1111111 Mode:0 STRAIGHT
[TRACK] X0111111 Mode:0 [BEEP] STOP_LINE_DETECTED
[TRACK] X0111111 Mode:0 STOP_LINE_DETECTED
[TRACK] X0111111 Mode:0 STOP_LINE_DETECTED
```

### **输出格式说明**
- **X1111111**: 7路传感器状态，X表示忽略的P_1
- **Mode:0**: 当前运行模式为0
- **STRAIGHT**: 直线行驶状态
- **STOP_LINE_DETECTED**: 检测到黑线并停止
- **[BEEP]**: 声光提示触发

## 🎮 操作方法

### **模式切换**
```c
// 按键控制
KEY0: sys_mode = (sys_mode+1)%4;  // 模式+1，循环0-3
KEY1: sys_mode = (sys_mode+2)%4;  // 模式+2，快速切换
KEY2: sys_ture = 1;               // 启动确认
```

### **启动步骤**
1. **上电**: 系统默认模式为0
2. **确认模式**: 观察串口输出"Mode:0"
3. **放置小车**: 将小车放在起始位置
4. **开始运行**: 小车自动开始直线行驶
5. **观察停止**: 遇到黑线时自动停止

## 🔧 参数调整

### **速度调整**
```c
// 当前速度：40%
move_forward(40);

// 更快速度
move_forward(50);  // 提高到50%

// 更慢速度  
move_forward(30);  // 降低到30%
```

### **检测灵敏度调整**
```c
// 当前：任意传感器检测到即停止
if(sensor_sum > 0) stop_motors();

// 更严格：至少2个传感器检测到
if(sensor_sum >= 2000) stop_motors();

// 更宽松：至少3个传感器检测到
if(sensor_sum >= 3000) stop_motors();
```

### **声光提示调整**
```c
// 当前：检测到黑线时触发
// 在声光控制部分已实现

// 可以在停止时额外触发
if(sensor_sum > 0) {
    stop_motors();
    // 额外的声光提示
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_SET);
    HAL_Delay(500);  // 更长的提示时间
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_RESET);
}
```

## 🎯 应用场景

### **适用场景**
- **基础测试**: 验证传感器和电机基本功能
- **距离测量**: 测量起点到黑线的距离
- **定点停车**: 在指定黑线位置精确停车
- **安全模式**: 作为安全的基础运行模式

### **竞赛应用**
- **起始阶段**: 从起点直行到第一条黑线
- **定点任务**: 需要在特定位置停止的任务
- **安全返回**: 紧急情况下的安全停止模式
- **调试模式**: 调试传感器和基本功能

## 📈 性能特点

### **优势**
- ✅ **简单可靠**: 逻辑简单，不易出错
- ✅ **响应快速**: 检测到黑线立即停止
- ✅ **精确停止**: 在黑线位置精确停车
- ✅ **易于调试**: 便于验证传感器功能
- ✅ **安全性高**: 遇到障碍(黑线)自动停止

### **局限性**
- ❌ **功能单一**: 只能直行，无转向能力
- ❌ **无避障**: 只能检测黑线，无其他避障
- ❌ **无循迹**: 不能跟随黑线行驶
- ❌ **停止后无动作**: 停止后需要人工干预

## 🔍 故障排除

### **小车不动**
```c
// 可能原因：
1. 模式不是0
2. 电机连接问题
3. 电源电压不足

// 检查方法：
- 观察串口输出"Mode:0"
- 检查电机连接和供电
- 手动测试move_forward函数
```

### **不停止**
```c
// 可能原因：
1. 传感器没有检测到黑线
2. 传感器高度不合适
3. 黑线对比度不够

// 检查方法：
- 观察串口传感器状态变化
- 调整传感器高度(2-5mm)
- 使用更黑的线或更白的背景
```

### **误停止**
```c
// 可能原因：
1. 传感器误触发
2. 环境光照变化
3. 地面有黑色区域

// 解决方法：
- 检查传感器连接
- 确保稳定光照
- 避免黑色地面
```

## 📊 测试验证

### **功能测试**
```
测试1: 基础直行
- 在白色地面上测试
- 观察小车直线行驶
- 确认速度和方向正确

测试2: 黑线检测
- 在前方放置黑线
- 观察小车接近黑线
- 确认在黑线前停止

测试3: 传感器测试
- 手动遮挡不同传感器
- 观察串口状态变化
- 确认所有传感器工作正常

测试4: 声光提示
- 观察检测到黑线时的提示
- 确认声光设备正常工作
```

### **性能测试**
```
测试项目: 停止精度
- 测量停止位置与黑线的距离
- 多次测试取平均值
- 精度应在±2cm内

测试项目: 响应时间
- 测量从检测到停止的时间
- 应在100ms内完成停止
```

## 🔧 代码优化

### **当前实现**
```c
// 简单的检测和停止
if(sensor_sum > 0) {
    stop_motors();
    my_printf(&huart1," STOP_LINE_DETECTED");
} else {
    move_forward(40);
    my_printf(&huart1," STRAIGHT");
}
```

### **优化建议**
```c
// 添加停止状态记录
static uint8_t stopped = 0;

if(sensor_sum > 0 && !stopped) {
    stop_motors();
    stopped = 1;  // 记录已停止
    my_printf(&huart1," STOP_LINE_DETECTED");
} else if(sensor_sum == 0 && !stopped) {
    move_forward(40);
    my_printf(&huart1," STRAIGHT");
} else if(stopped) {
    // 已停止状态
    my_printf(&huart1," STOPPED");
}
```

## 📁 相关文档

### **参考文档**
- `Simple_Line_Following_Control.md` - 简单循迹控制
- `7_Sensor_Configuration.md` - 7路传感器配置
- `Sound_Light_Control_Implementation.md` - 声光控制

### **代码文件**
- `APP/scheduler.c` - 主控制逻辑
- `APP/Gray.c` - 传感器读取
- `APP/car.c` - 运动控制
- `APP/key.c` - 按键控制

---

**实现完成时间**: 2025-06-20  
**模式类型**: 基础直行模式  
**特点**: 简单可靠 + 精确停止 + 安全性高  
**状态**: ✅ 已实现
