#ifndef MOTION_CONTROL_H
#define MOTION_CONTROL_H

#include <stdint.h>

// ========== 运动控制函数声明 ==========

/**
 * @brief 前进运动
 * @param speed 速度值 (1-99)
 * @note 双电机同速前进
 */
void move_forward(int8_t speed);

/**
 * @brief 后退运动  
 * @param speed 速度值 (1-99)
 * @note 双电机同速后退
 */
void move_backward(int8_t speed);

/**
 * @brief 左转运动 (差速转向)
 * @param speed 基础速度值 (1-99)
 * @note 左轮半速，右轮全速
 */
void turn_left(int8_t speed);

/**
 * @brief 右转运动 (差速转向)
 * @param speed 基础速度值 (1-99)  
 * @note 左轮全速，右轮半速
 */
void turn_right(int8_t speed);

/**
 * @brief 停止所有电机
 * @note 双电机立即停止
 */
void stop_motors(void);

/**
 * @brief 自定义双电机速度控制
 * @param left_speed 左电机速度 (-99到+99)
 * @param right_speed 右电机速度 (-99到+99)
 * @note 完全自定义的双电机控制
 */
void move_custom(int8_t left_speed, int8_t right_speed);

// ========== 运动控制宏定义 ==========

// 预定义速度等级
#define SPEED_SLOW    20    // 慢速
#define SPEED_MEDIUM  50    // 中速  
#define SPEED_FAST    80    // 快速
#define SPEED_MAX     99    // 最大速度

// 快捷运动宏
#define FORWARD_SLOW()      move_forward(SPEED_SLOW)
#define FORWARD_MEDIUM()    move_forward(SPEED_MEDIUM)
#define FORWARD_FAST()      move_forward(SPEED_FAST)

#define BACKWARD_SLOW()     move_backward(SPEED_SLOW)
#define BACKWARD_MEDIUM()   move_backward(SPEED_MEDIUM)
#define BACKWARD_FAST()     move_backward(SPEED_FAST)

#define LEFT_SLOW()         turn_left(SPEED_SLOW)
#define LEFT_MEDIUM()       turn_left(SPEED_MEDIUM)
#define LEFT_FAST()         turn_left(SPEED_FAST)

#define RIGHT_SLOW()        turn_right(SPEED_SLOW)
#define RIGHT_MEDIUM()      turn_right(SPEED_MEDIUM)
#define RIGHT_FAST()        turn_right(SPEED_FAST)

#define STOP()              stop_motors()

// 高级运动控制宏
#define SPIN_LEFT(speed)    move_custom(-(speed), (speed))   // 原地左转
#define SPIN_RIGHT(speed)   move_custom((speed), -(speed))   // 原地右转
#define CURVE_LEFT(speed)   move_custom((speed)/3, (speed))  // 大弯左转
#define CURVE_RIGHT(speed)  move_custom((speed), (speed)/3)  // 大弯右转

#endif // MOTION_CONTROL_H
