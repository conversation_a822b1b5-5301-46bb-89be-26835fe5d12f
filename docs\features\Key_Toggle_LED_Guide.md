# 🔘 按键切换LED亮灭功能指南

## 📊 功能概述

实现了按键切换LED亮灭的功能：
- **按一下**: LED亮
- **再按一下**: LED灭
- **循环切换**: 持续按键可以在亮/灭之间切换

## 🔧 技术实现

### **按键检测逻辑**
```c
void key_task(void)
{
    static uint8_t led_state = 0;  // 静态变量保持LED状态
    key_val = Key_Read();
    key_down = key_val&(key_val ^ key_old);  // 检测按键按下
    key_up = ~key_val&(key_val ^ key_old);   // 检测按键释放
    key_old = key_val;
    
    switch(key_down)
    {
        case 2:  // KEY2按下
            led_state ^= 1;  // 状态切换 (0→1 或 1→0)
            if(led_state == 1)
                HAL_GPIO_WritePin(LED2_GPIO_Port,LED2_Pin,GPIO_PIN_RESET); // LED亮
            else
                HAL_GPIO_WritePin(LED2_GPIO_Port,LED2_Pin,GPIO_PIN_SET);   // LED灭
        break;
    }
}
```

### **状态保持机制**
- **静态变量**: `static uint8_t led_state = 0`
- **状态切换**: `led_state ^= 1` (异或操作实现0/1切换)
- **持久保存**: 静态变量在函数调用间保持值

## ⚙️ 硬件配置

### **按键连接**
- **按键**: KEY2
- **引脚**: 根据key.h配置
- **触发**: 按下时为低电平 (GPIO_PIN_RESET)
- **检测频率**: 10ms检查一次

### **LED连接**
- **LED**: LED2
- **引脚**: 根据GPIO配置
- **亮**: GPIO_PIN_RESET (低电平)
- **灭**: GPIO_PIN_SET (高电平)

### **初始状态**
- **系统启动**: LED灭 (GPIO_PIN_SET)
- **按键状态**: led_state = 0
- **首次按键**: LED亮

## 📈 工作流程

### **按键检测流程**
```
1. 读取按键状态 → Key_Read()
2. 计算按键变化 → key_down = key_val&(key_val ^ key_old)
3. 检测按下事件 → case 2
4. 切换LED状态 → led_state ^= 1
5. 更新LED输出 → HAL_GPIO_WritePin()
```

### **状态转换图**
```
初始状态: LED灭 (led_state=0)
    ↓ 按KEY2
状态1: LED亮 (led_state=1)
    ↓ 按KEY2
状态2: LED灭 (led_state=0)
    ↓ 按KEY2
状态1: LED亮 (led_state=1)
    ↓ ...循环
```

## 🎯 功能特点

### **防抖动设计**
- **硬件防抖**: 按键检测逻辑自动处理抖动
- **边沿检测**: 只在按键按下瞬间触发
- **状态记忆**: 记录上次按键状态，避免重复触发

### **响应速度**
- **检测频率**: 10ms检查一次
- **响应时间**: <20ms
- **稳定性**: 可靠的状态切换

### **兼容性**
- **不冲突**: 与其他功能(电机控制、串口输出)不冲突
- **独立控制**: LED完全由按键控制
- **系统稳定**: 不影响其他任务运行

## 🔄 与原系统的变化

### **修改的内容**
1. **key.c**: 修改按键处理逻辑，实现状态切换
2. **scheduler.c**: 禁用LED自动呼吸效果
3. **LED控制**: 从PWM控制改为GPIO控制

### **保留的功能**
- ✅ **电机控制**: 双电机正常运行
- ✅ **串口输出**: 速度数据正常显示
- ✅ **编码器**: 正常读取编码器数据
- ✅ **调度器**: 所有任务正常调度

### **移除的功能**
- ❌ **LED呼吸**: 不再自动呼吸闪烁
- ❌ **PWM控制**: LED不再使用PWM控制

## 🎮 使用方法

### **基本操作**
1. **系统启动**: LED处于灭的状态
2. **按一下KEY2**: LED亮起
3. **再按一下KEY2**: LED熄灭
4. **继续按KEY2**: 在亮/灭之间循环切换

### **测试步骤**
1. **上电测试**: 确认LED初始为灭
2. **按键测试**: 按KEY2确认LED亮
3. **切换测试**: 再按KEY2确认LED灭
4. **循环测试**: 多次按键确认正常切换

## 📊 技术细节

### **按键检测算法**
```c
// 按键状态检测
key_val = Key_Read();                    // 当前状态
key_down = key_val&(key_val ^ key_old);  // 按下检测
key_up = ~key_val&(key_val ^ key_old);   // 释放检测
key_old = key_val;                       // 更新历史状态
```

### **状态切换算法**
```c
led_state ^= 1;  // 异或切换: 0→1, 1→0
```

### **GPIO控制**
```c
// LED亮 (低电平有效)
HAL_GPIO_WritePin(LED2_GPIO_Port,LED2_Pin,GPIO_PIN_RESET);

// LED灭 (高电平)
HAL_GPIO_WritePin(LED2_GPIO_Port,LED2_Pin,GPIO_PIN_SET);
```

## 🔧 调试信息

### **如果按键无响应**
1. **检查硬件**: 确认KEY2连接正确
2. **检查配置**: 确认GPIO配置正确
3. **检查频率**: 确认key_task正常调度

### **如果LED不亮**
1. **检查GPIO**: 确认LED2引脚配置
2. **检查电路**: 确认LED硬件连接
3. **检查极性**: 确认高低电平逻辑

### **如果状态不保持**
1. **检查静态变量**: 确认led_state为static
2. **检查逻辑**: 确认状态切换逻辑正确
3. **检查调度**: 确认任务正常运行

## ⚠️ 注意事项

### **硬件注意**
- 确保按键连接稳定
- 确保LED电路正常
- 避免按键抖动过大

### **软件注意**
- 静态变量确保状态保持
- 按键检测逻辑防止误触发
- GPIO控制与PWM控制不要混用

### **系统注意**
- 按键任务频率不宜过高
- LED控制不影响其他功能
- 保持系统整体稳定性

## 📈 扩展功能

### **可能的扩展**
1. **长按功能**: 检测长按实现其他功能
2. **多键组合**: 支持多个按键组合
3. **LED模式**: 支持闪烁、呼吸等模式
4. **状态指示**: 通过LED显示系统状态

### **实现建议**
```c
// 长按检测示例
static uint32_t press_time = 0;
if(key_val == 2) {
    if(press_time == 0) press_time = HAL_GetTick();
    else if(HAL_GetTick() - press_time > 1000) {
        // 长按1秒后的操作
    }
} else {
    press_time = 0;
}
```

## 📞 技术支持

如需帮助：
1. 检查按键硬件连接
2. 确认GPIO配置正确
3. 验证任务调度正常
4. 参考调试信息排查

---

**创建时间**: 2025-06-20  
**功能**: 按键切换LED亮灭  
**状态**: ✅ 已实现
