# 📊 MPU6050数据读取输出文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 已完成
- **目的**: 在串口输出中添加MPU6050姿态角数据

## 🎯 输出格式

### 新的串口输出格式
```
L:25.3 R:-18.7 | P:12.5 R:-3.2 Y:45.8
L:30.1 R:22.4 | P:10.1 R:-2.8 Y:46.2
L:0.0 R:0.0 | P:0.5 R:0.1 Y:46.2
```

### 格式说明
- **L**: 左轮速度 (RPM)
- **R**: 右轮速度 (RPM，已反向)
- **P**: 俯仰角 Pitch (度)
- **R**: 横滚角 Roll (度)
- **Y**: 偏航角 Yaw (度，连续值)
- **精度**: 所有数据保留1位小数
- **频率**: 50ms更新一次 (20Hz)

## 🔧 实现代码

### 核心输出代码
```c
// 简洁的左右轮速度输出 + MPU6050姿态角
my_printf(&huart1, "L:%.1f R:%.1f | P:%.1f R:%.1f Y:%.1f\r\n", 
          rpm_l, -rpm_r, Pitch, Roll, Yaw);
```

### 修改位置
- **文件**: APP/scheduler.c
- **函数**: uart_task()
- **行号**: 第78-80行
- **周期**: 50ms执行一次

## 📊 数据含义

### 电机数据
- **rpm_l**: 左轮转速 (转/分钟)
- **-rpm_r**: 右轮转速 (转/分钟，方向已反向)
- **正值**: 前进方向
- **负值**: 后退方向

### MPU6050姿态角数据
- **Pitch**: 俯仰角，前倾为负，后仰为正
- **Roll**: 横滚角，左倾为负，右倾为正
- **Yaw**: 偏航角，连续值，可超过±180°

## 🎮 数据解读

### 1. 静止状态
```
L:0.0 R:0.0 | P:0.5 R:0.1 Y:46.2
```
- 电机静止
- 小车基本水平 (俯仰角和横滚角接近0)
- 偏航角保持稳定

### 2. 直线前进
```
L:25.0 R:-25.0 | P:2.1 R:0.3 Y:46.5
```
- 左右轮速度对称
- 轻微前倾 (可能是加速导致)
- 偏航角缓慢变化

### 3. 右转运动
```
L:30.0 R:-10.0 | P:1.5 R:5.2 Y:52.3
```
- 左轮快，右轮慢 (右转)
- 向右倾斜 (离心力作用)
- 偏航角增加 (右转)

### 4. 左转运动
```
L:10.0 R:-30.0 | P:1.2 R:-4.8 Y:40.1
```
- 右轮快，左轮慢 (左转)
- 向左倾斜 (离心力作用)
- 偏航角减少 (左转)

### 5. 上坡行驶
```
L:35.0 R:-35.0 | P:15.3 R:0.2 Y:46.8
```
- 电机功率增加 (克服重力)
- 明显后仰 (上坡角度)
- 横滚角和偏航角基本稳定

### 6. 下坡行驶
```
L:15.0 R:-15.0 | P:-12.8 R:-0.1 Y:46.3
```
- 电机功率减少 (重力辅助)
- 明显前倾 (下坡角度)
- 横滚角和偏航角基本稳定

## 📈 数据分析应用

### 1. 运动状态判断
```c
// 基于电机和姿态数据判断运动状态
if(fabs(rpm_l) < 1.0f && fabs(rpm_r) < 1.0f) {
    // 静止状态
    if(fabs(Pitch) > 5.0f || fabs(Roll) > 5.0f) {
        // 静止但倾斜，可能在斜坡上
    }
} else {
    // 运动状态
    if(fabs(Pitch) > 10.0f) {
        // 上坡或下坡
    }
    if(fabs(Roll) > 5.0f) {
        // 转弯或侧倾
    }
}
```

### 2. 转向分析
```c
// 结合电机和偏航角分析转向
float speed_diff = rpm_l - (-rpm_r);  // 左右轮速度差
static float last_yaw = 0;
float yaw_rate = (Yaw - last_yaw) / 0.05f;  // 偏航角速度

if(speed_diff > 5.0f && yaw_rate > 0) {
    // 左转：左轮快且偏航角增加
} else if(speed_diff < -5.0f && yaw_rate < 0) {
    // 右转：右轮快且偏航角减少
}
```

### 3. 稳定性监控
```c
// 监控小车稳定性
if(fabs(Roll) > 20.0f) {
    // 横滚角过大，可能翻车
    emergency_stop();
}

if(fabs(Pitch) > 30.0f) {
    // 俯仰角过大，可能前翻或后翻
    emergency_stop();
}
```

### 4. 路面状况检测
```c
// 基于姿态角变化检测路面
static float pitch_history[10];
static uint8_t history_index = 0;

pitch_history[history_index] = Pitch;
history_index = (history_index + 1) % 10;

// 计算俯仰角方差
float pitch_variance = calculate_variance(pitch_history, 10);
if(pitch_variance > 5.0f) {
    // 路面不平，颠簸较大
}
```

## 🔧 调试应用

### 1. 循迹算法调试
- **观察转向**: 结合电机速度和偏航角变化
- **检测偏离**: 偏航角持续偏离目标方向
- **优化参数**: 根据实际转向效果调整PID参数

### 2. 电机控制调试
- **速度对称性**: 直线行驶时左右轮速度是否对称
- **响应速度**: 电机速度变化与姿态角变化的关系
- **功率分配**: 上下坡时的功率调整效果

### 3. 机械结构调试
- **重心位置**: 通过横滚角判断重心是否居中
- **轮胎状况**: 异常的横滚角可能表示轮胎问题
- **机械磨损**: 持续的偏航可能表示机械不对称

## ⚠️ 注意事项

### 1. 数据精度
- 姿态角精度约±1°
- 偏航角会有累积误差
- 电机速度精度约±0.1 RPM

### 2. 更新频率
- 50ms更新周期适合大多数应用
- MPU6050内部采样率为50Hz
- 数据同步性良好

### 3. 环境影响
- 振动会影响姿态角精度
- 磁场干扰可能影响偏航角
- 温度变化可能影响传感器精度

### 4. 数据解读
- 偏航角为连续值，注意数值跳变
- 姿态角受加速度影响，静止时更准确
- 结合多个数据源进行综合判断

## 🔄 未来扩展

### 1. 数据记录
```c
// CSV格式便于分析
my_printf(&huart1, "%.1f,%.1f,%.1f,%.1f,%.1f\r\n", 
          rpm_l, -rpm_r, Pitch, Roll, Yaw);
```

### 2. 可选详细模式
```c
// 详细模式包含更多信息
my_printf(&huart1, "L:%.1f R:%.1f | P:%.1f R:%.1f Y:%.1f | T:%.1f\r\n", 
          rpm_l, -rpm_r, Pitch, Roll, Yaw, temperature);
```

### 3. 实时图形显示
- 配合上位机软件
- 实时姿态角曲线
- 运动轨迹显示

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**输出格式**: L:xx.x R:xx.x | P:xx.x R:xx.x Y:xx.x  
**技术支持**: STM32循迹小车开发团队
