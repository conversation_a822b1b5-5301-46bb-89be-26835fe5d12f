# 🎯 基于2024_H_Car的MPU6050移植完成文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 已完成
- **目的**: 仿照2024_H_Car（New）的MPU6050实现完整移植

## 🎯 移植概述

### 参考源码
- **源项目**: 2024_H_Car（New）
- **参考文件**: mpu6050_app.c, mpu6050_driver.c, MPU6050模块
- **移植方式**: 完整仿照，适配我们的硬件配置

### 硬件适配
- **I2C引脚**: PE14 (SCL), PE15 (SDA)
- **通信方式**: 软件I2C模拟
- **电源**: 3.3V供电
- **地址**: 0x68 (AD0接地)

## 📊 移植文件结构

### 新增文件列表

#### 1. 应用层文件
```
APP/mpu6050_app.h        - MPU6050应用层头文件
APP/mpu6050_app.c        - MPU6050应用层实现
APP/mpu6050_driver.h     - MPU6050驱动层头文件  
APP/mpu6050_driver.c     - MPU6050驱动层实现
```

#### 2. 模块层文件
```
APP/mpu6050.h           - MPU6050核心模块头文件
APP/mpu6050.c           - MPU6050核心模块实现
APP/IIC.h               - 软件I2C头文件
APP/IIC.c               - 软件I2C实现
APP/inv_mpu.h           - DMP功能头文件
APP/inv_mpu.c           - DMP功能实现
```

### 修改文件列表

#### 1. 任务调度器
```
APP/scheduler.h         - 添加mpu_task()声明
APP/scheduler.c         - 集成MPU6050任务
```

#### 2. 主程序
```
Core/Src/main.c         - 添加mpu6050_app.h包含
```

## 🔧 核心功能实现

### 1. 软件I2C适配 (IIC.c/IIC.h)

#### GPIO配置
```c
#define GPIO_PORT_IIC     GPIOE                       /* GPIO端口 */
#define IIC_SCL_PIN       GPIO_PIN_14                  /* SCL时钟线 */
#define IIC_SDA_PIN       GPIO_PIN_15                  /* SDA数据线 */
```

#### 核心函数
```c
void IIC_GPIO_Init(void);           // I2C GPIO初始化
void IIC_Start(void);               // 起始信号
void IIC_Stop(void);                // 停止信号
void IIC_Send_Byte(uint8_t data);   // 发送字节
uint8_t IIC_Read_Byte(uint8_t ack); // 读取字节
uint8_t IIC_Wait_Ack(void);         // 等待应答
```

### 2. MPU6050核心模块 (mpu6050.c/mpu6050.h)

#### 寄存器定义
```c
#define MPU_ADDR                0X68    // MPU6050地址
#define MPU_PWR_MGMT1_REG       0X6B    // 电源管理寄存器1
#define MPU_GYRO_CFG_REG        0X1B    // 陀螺仪配置寄存器
#define MPU_ACCEL_CFG_REG       0X1C    // 加速度计配置寄存器
```

#### 核心函数
```c
uint8_t MPU_Init(void);                                    // 初始化
uint8_t MPU_Get_Gyroscope(short *gx,short *gy,short *gz); // 读陀螺仪
uint8_t MPU_Get_Accelerometer(short *ax,short *ay,short *az); // 读加速度计
short MPU_Get_Temperature(void);                          // 读温度
```

### 3. DMP功能 (inv_mpu.c/inv_mpu.h)

#### 简化DMP实现
```c
uint8_t mpu_dmp_init(void);                               // DMP初始化
uint8_t mpu_dmp_get_data(float *pitch,float *roll,float *yaw); // 获取姿态角
```

#### 姿态解算
- **俯仰角**: 基于加速度计计算
- **横滚角**: 基于加速度计计算  
- **偏航角**: 基于陀螺仪积分计算

### 4. 驱动层功能 (mpu6050_driver.c/mpu6050_driver.h)

#### 陀螺仪校准
```c
void MPU_Get_Gyro_Offset(short* gx_offset, short* gy_offset, short* gz_offset);
```

#### 连续偏航角转换
```c
float convert_to_continuous_yaw(float current_yaw);
```

### 5. 应用层接口 (mpu6050_app.c/mpu6050_app.h)

#### 全局变量
```c
extern float Pitch, Roll, Yaw;  // 姿态角全局变量
```

#### 应用接口
```c
void Mpu6050_Init(void);        // 应用层初始化
void Mpu6050_Task(void);        // 应用层任务
```

## 🔄 任务调度集成

### 任务周期
- **MPU6050任务**: 20ms周期执行
- **数据更新**: 50Hz更新频率
- **姿态计算**: 实时姿态角计算

### 调度器集成
```c
void scheduler_init()
{
    // 初始化MPU6050
    Mpu6050_Init();
}

void scheduler_run()
{
    // MPU6050任务 - 每20ms执行一次
    if(current_time - last_mpu_time >= 20) {
        mpu_task();
        last_mpu_time = current_time;
    }
}

void mpu_task()
{
    // 执行MPU6050数据读取和处理
    Mpu6050_Task();
}
```

## 📊 数据访问接口

### 全局数据变量
```c
extern float Pitch, Roll, Yaw;  // 在mpu6050_app.h中声明
```

### 数据使用示例
```c
#include "mpu6050_app.h"

// 获取姿态角数据
float current_pitch = Pitch;    // 俯仰角 (度)
float current_roll = Roll;      // 横滚角 (度)  
float current_yaw = Yaw;        // 偏航角 (度，连续值)
```

### 数据特性
- **俯仰角**: 前负后正，范围 ±90°
- **横滚角**: 左负右正，范围 ±90°
- **偏航角**: 连续值，可超过 ±180°

## 🎮 应用场景

### 1. 姿态监控
```c
// 检测小车倾斜
if(fabs(Pitch) > 30.0f || fabs(Roll) > 30.0f) {
    // 小车倾斜过大，紧急停止
    stop_motors();
}
```

### 2. 方向控制
```c
// 基于偏航角的方向控制
float yaw_error = target_yaw - Yaw;
int8_t yaw_correction = (int8_t)(yaw_error * 0.5f);
left_speed -= yaw_correction;
right_speed += yaw_correction;
```

### 3. 运动检测
```c
// 检测快速转动
static float last_yaw = 0;
float yaw_rate = (Yaw - last_yaw) / 0.02f;  // 20ms周期
if(fabs(yaw_rate) > 90.0f) {
    // 快速转动检测
}
last_yaw = Yaw;
```

## ⚙️ 配置参数

### MPU6050配置
```c
MPU_Set_Gyro_Fsr(3);        // ±2000dps
MPU_Set_Accel_Fsr(0);       // ±2g
MPU_Set_Rate(50);           // 50Hz采样率
MPU_Set_LPF(42);            // 42Hz低通滤波
```

### I2C时序配置
```c
// IIC_Delay()函数中的循环次数
for (i = 0; i < 10; i++);   // 约200KHz I2C频率
```

## ⚠️ 注意事项

### 1. 硬件连接
- 确保PE14/PE15引脚连接正确
- 检查3.3V电源供电
- 添加4.7kΩ上拉电阻

### 2. 软件配置
- GPIO已在CubeMX中配置为开漏输出
- I2C时序适合大多数应用
- DMP功能为简化版本

### 3. 数据精度
- 姿态角精度约±1°
- 偏航角会有累积误差
- 建议定期重新校准

### 4. 性能考虑
- 20ms任务周期适中
- I2C通信约占用1-2ms
- 不影响其他任务执行

## 🔄 与2024_H_Car的差异

### 相同点
- **函数接口**: 完全相同的API
- **数据格式**: 相同的数据结构
- **调用方式**: 相同的使用方法

### 差异点
- **I2C引脚**: PE14/PE15 vs 原项目引脚
- **DMP实现**: 简化版本 vs 完整DMP
- **任务集成**: 集成到我们的scheduler

## 🎯 测试建议

### 1. 基础测试
- 检查I2C通信是否正常
- 验证设备ID读取
- 测试原始数据读取

### 2. 功能测试
- 测试姿态角计算
- 验证连续偏航角转换
- 检查数据更新频率

### 3. 应用测试
- 结合循迹算法测试
- 验证姿态控制效果
- 测试长时间稳定性

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**移植状态**: ✅ 完成，基于2024_H_Car蓝本  
**技术支持**: STM32循迹小车开发团队
