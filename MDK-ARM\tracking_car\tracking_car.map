Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) for DMA2_Stream7_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to tim.o(i.MX_TIM14_Init) for MX_TIM14_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    main.o(i.main) refers to gray.o(i.Gray_Init) for Gray_Init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to tim.o(.bss) for htim14
    main.o(i.main) refers to my_uart.o(.bss) for uart_rx_buffer
    main.o(i.main) refers to usart.o(.bss) for huart1
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM14_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM14_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM14_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM3_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) refers to usart.o(.bss) for hdma_usart1_tx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to my_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to my_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to my_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to my_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to my_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    scheduler.o(i.gray_task) refers to gray.o(i.Gray_Task) for Gray_Task
    scheduler.o(i.gray_task) refers to gray.o(i.Gray_get) for Gray_get
    scheduler.o(i.gray_task) refers to gray.o(i.Gray_Get_Position_Error) for Gray_Get_Position_Error
    scheduler.o(i.gray_task) refers to gray.o(i.Gray_Get_Active_Sensor_Count) for Gray_Get_Active_Sensor_Count
    scheduler.o(i.gray_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.gray_task) refers to motor_app.o(i.Motor_App_Stop) for Motor_App_Stop
    scheduler.o(i.gray_task) refers to motor_app.o(i.Motor_App_Forward) for Motor_App_Forward
    scheduler.o(i.gray_task) refers to mpu6050_app.o(i.Reset_Target_Direction) for Reset_Target_Direction
    scheduler.o(i.gray_task) refers to mpu6050_app.o(i.Get_Direction_Correction) for Get_Direction_Correction
    scheduler.o(i.gray_task) refers to motor_app.o(i.Motor_App_Set_Speed) for Motor_App_Set_Speed
    scheduler.o(i.gray_task) refers to scheduler.o(.data) for .data
    scheduler.o(i.gray_task) refers to key.o(.data) for sys_mode
    scheduler.o(i.motor_task) refers to motor_app.o(i.Motor_App_Forward) for Motor_App_Forward
    scheduler.o(i.motor_task) refers to motor_app.o(i.Motor_App_Task) for Motor_App_Task
    scheduler.o(i.mpu_task) refers to mpu6050_app.o(i.Mpu6050_Task) for Mpu6050_Task
    scheduler.o(i.scheduler_init) refers to motor_app.o(i.Motor_App_Init) for Motor_App_Init
    scheduler.o(i.scheduler_init) refers to mpu6050_app.o(i.Mpu6050_Init) for Mpu6050_Init
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(i.uart_task) refers to gray.o(i.Gray_Get_Position_Error) for Gray_Get_Position_Error
    scheduler.o(i.uart_task) refers to gray.o(i.Gray_Get_Active_Sensor_Count) for Gray_Get_Active_Sensor_Count
    scheduler.o(i.uart_task) refers to mpu6050_app.o(i.Get_Yaw_Error) for Get_Yaw_Error
    scheduler.o(i.uart_task) refers to mpu6050_app.o(i.Get_Direction_Correction) for Get_Direction_Correction
    scheduler.o(i.uart_task) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(i.uart_task) refers to my_uart.o(i.my_printf_nb) for my_printf_nb
    scheduler.o(i.uart_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.uart_task) refers to memseta.o(.text) for __aeabi_memclr
    scheduler.o(i.uart_task) refers to scheduler.o(.data) for .data
    scheduler.o(i.uart_task) refers to mpu6050_app.o(.data) for Yaw
    scheduler.o(i.uart_task) refers to key.o(.data) for sys_ture
    scheduler.o(i.uart_task) refers to scheduler.o(.conststring) for .conststring
    scheduler.o(i.uart_task) refers to usart.o(.bss) for huart1
    scheduler.o(i.uart_task) refers to my_uart.o(.data) for uart_rx_index
    scheduler.o(i.uart_task) refers to my_uart.o(.bss) for uart_rx_buffer
    scheduler.o(.data) refers to key.o(i.key_task) for key_task
    scheduler.o(.data) refers to scheduler.o(i.uart_task) for uart_task
    scheduler.o(.data) refers to scheduler.o(i.motor_task) for motor_task
    scheduler.o(.data) refers to scheduler.o(i.mpu_task) for mpu_task
    scheduler.o(.data) refers to scheduler.o(i.gray_task) for gray_task
    key.o(i.Key_Read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.key_task) refers to key.o(i.Key_Read) for Key_Read
    key.o(i.key_task) refers to key.o(.data) for .data
    my_uart.o(i.HAL_UART_ErrorCallback) refers to my_uart.o(.data) for .data
    my_uart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_uart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal.o(.data) for uwTick
    my_uart.o(i.HAL_UART_RxCpltCallback) refers to my_uart.o(.data) for .data
    my_uart.o(i.HAL_UART_RxCpltCallback) refers to my_uart.o(.bss) for .bss
    my_uart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    my_uart.o(i.HAL_UART_TxCpltCallback) refers to my_uart.o(.data) for .data
    my_uart.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_uart.o(i.my_printf) refers to memcpya.o(.text) for __aeabi_memcpy
    my_uart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    my_uart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_uart.o(i.my_printf) refers to my_uart.o(.data) for .data
    my_uart.o(i.my_printf) refers to my_uart.o(.bss) for .bss
    my_uart.o(i.my_printf_nb) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_uart.o(i.my_printf_nb) refers to memcpya.o(.text) for __aeabi_memcpy
    my_uart.o(i.my_printf_nb) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    my_uart.o(i.my_printf_nb) refers to my_uart.o(.data) for .data
    my_uart.o(i.my_printf_nb) refers to my_uart.o(.bss) for .bss
    my_uart.o(i.uart_dma_is_busy) refers to my_uart.o(.data) for .data
    my_uart.o(i.uart_dma_wait_complete) refers to my_uart.o(.data) for .data
    gray.o(i.Gray_Get_Active_Sensor_Count) refers to gray.o(.data) for .data
    gray.o(i.Gray_Get_Position_Error) refers to gray.o(.data) for .data
    gray.o(i.Gray_Init) refers to gray.o(.data) for .data
    gray.o(i.Gray_Task) refers to gray.o(i.Gray_get) for Gray_get
    gray.o(i.Gray_Task) refers to gray.o(.data) for .data
    gray.o(i.Gray_get) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    motor_app.o(i.Motor_App_Backward) refers to motor_driver.o(i.Motor_Set_Speed) for Motor_Set_Speed
    motor_app.o(i.Motor_App_Backward) refers to motor_app.o(.bss) for .bss
    motor_app.o(i.Motor_App_Forward) refers to motor_driver.o(i.Motor_Set_Speed) for Motor_Set_Speed
    motor_app.o(i.Motor_App_Forward) refers to motor_app.o(.bss) for .bss
    motor_app.o(i.Motor_App_Init) refers to motor_driver.o(i.Motor_Config_Init) for Motor_Config_Init
    motor_app.o(i.Motor_App_Init) refers to tim.o(.bss) for htim3
    motor_app.o(i.Motor_App_Init) refers to motor_app.o(.bss) for .bss
    motor_app.o(i.Motor_App_Set_Speed) refers to motor_driver.o(i.Motor_Set_Speed) for Motor_Set_Speed
    motor_app.o(i.Motor_App_Set_Speed) refers to motor_app.o(.bss) for .bss
    motor_app.o(i.Motor_App_Stop) refers to motor_driver.o(i.Motor_Stop) for Motor_Stop
    motor_app.o(i.Motor_App_Stop) refers to motor_app.o(.bss) for .bss
    motor_app.o(i.Motor_App_Turn_Left) refers to motor_driver.o(i.Motor_Set_Speed) for Motor_Set_Speed
    motor_app.o(i.Motor_App_Turn_Left) refers to motor_app.o(.bss) for .bss
    motor_app.o(i.Motor_App_Turn_Right) refers to motor_driver.o(i.Motor_Set_Speed) for Motor_Set_Speed
    motor_app.o(i.Motor_App_Turn_Right) refers to motor_app.o(.bss) for .bss
    motor_driver.o(i.Motor_Brake) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Config_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Config_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver.o(i.Motor_Set_Speed) refers to motor_driver.o(i.Motor_Limit_Speed) for Motor_Limit_Speed
    motor_driver.o(i.Motor_Set_Speed) refers to motor_driver.o(i.Motor_Dead_Compensation) for Motor_Dead_Compensation
    motor_driver.o(i.Motor_Set_Speed) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    mpu6050_app.o(i.Direction_Control_Init) refers to mpu6050_app.o(.data) for .data
    mpu6050_app.o(i.Direction_Control_Update) refers to mpu6050_app.o(.data) for .data
    mpu6050_app.o(i.Get_Direction_Correction) refers to mpu6050_app.o(i.Get_Yaw_Error) for Get_Yaw_Error
    mpu6050_app.o(i.Get_Yaw_Error) refers to mpu6050_app.o(.data) for .data
    mpu6050_app.o(i.Mpu6050_Init) refers to mpu6050.o(i.MPU_Init) for MPU_Init
    mpu6050_app.o(i.Mpu6050_Init) refers to inv_mpu.o(i.mpu_dmp_init) for mpu_dmp_init
    mpu6050_app.o(i.Mpu6050_Init) refers to mpu6050_app.o(i.Direction_Control_Init) for Direction_Control_Init
    mpu6050_app.o(i.Mpu6050_Init) refers to mpu6050_app.o(.data) for .data
    mpu6050_app.o(i.Mpu6050_Task) refers to inv_mpu.o(i.mpu_dmp_get_data) for mpu_dmp_get_data
    mpu6050_app.o(i.Mpu6050_Task) refers to mpu6050_app.o(i.convert_to_continuous_yaw) for convert_to_continuous_yaw
    mpu6050_app.o(i.Mpu6050_Task) refers to mpu6050_app.o(i.Direction_Control_Update) for Direction_Control_Update
    mpu6050_app.o(i.Mpu6050_Task) refers to mpu6050_app.o(.data) for .data
    mpu6050_app.o(i.Reset_Target_Direction) refers to mpu6050_app.o(.data) for .data
    mpu6050_app.o(i.Set_Target_Direction) refers to mpu6050_app.o(.data) for .data
    mpu6050_app.o(i.convert_to_continuous_yaw) refers to mpu6050_app.o(.data) for .data
    mpu6050.o(i.MPU_Get_Accelerometer) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Gyro_Offset) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Gyroscope) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to dflti.o(.text) for __aeabi_i2d
    mpu6050.o(i.MPU_Get_Temperature) refers to ddiv.o(.text) for __aeabi_ddiv
    mpu6050.o(i.MPU_Get_Temperature) refers to dadd.o(.text) for __aeabi_dadd
    mpu6050.o(i.MPU_Get_Temperature) refers to d2f.o(.text) for __aeabi_d2f
    mpu6050.o(i.MPU_Init) refers to iic.o(i.IIC_GPIO_Init) for IIC_GPIO_Init
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Gyro_Fsr) for MPU_Set_Gyro_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Accel_Fsr) for MPU_Set_Accel_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Rate) for MPU_Set_Rate
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Read_Byte) for MPU_Read_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Read_Byte) for IIC_Read_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Stop) for IIC_Stop
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Stop) for IIC_Stop
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Read_Byte) for IIC_Read_Byte
    mpu6050.o(i.MPU_Set_Accel_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Gyro_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_LPF) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Set_LPF) for MPU_Set_LPF
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Stop) for IIC_Stop
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Stop) for IIC_Stop
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(i.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(i.get_accel_prod_shift) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.get_accel_prod_shift) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.get_st_biases) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.get_st_biases) refers to ldiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.gyro_self_test) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(i.inv_row_2_scale) for inv_row_2_scale
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_dmp_get_data) refers to inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(i.mpu_dmp_get_data) refers to f2d.o(.text) for __aeabi_f2d
    inv_mpu.o(i.mpu_dmp_get_data) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    inv_mpu.o(i.mpu_dmp_get_data) refers to dmul.o(.text) for __aeabi_dmul
    inv_mpu.o(i.mpu_dmp_get_data) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(i.mpu_dmp_get_data) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    inv_mpu.o(i.mpu_dmp_init) refers to iic.o(i.IIC_GPIO_Init) for IIC_GPIO_Init
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_init) for mpu_init
    inv_mpu.o(i.mpu_dmp_init) refers to mpu6050.o(i.MPU_Get_Gyro_Offset) for MPU_Get_Gyro_Offset
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.run_self_test) for run_self_test
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_sens) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_dmp_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_fifo_config) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_sens) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_int_status) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_int_status) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_lpf) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_power_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_sample_rate) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_temperature) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(i.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(i.mpu_load_firmware) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_lp_accel_mode) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_fifo) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_fifo_stream) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_mem) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_reg_dump) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_reg_dump) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_reset_fifo) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_reset_fifo) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_reset_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.get_st_biases) for get_st_biases
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.accel_self_test) for accel_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.gyro_self_test) for gyro_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_accel_bias) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_accel_fsr) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_accel_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_bypass) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_bypass) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_set_dmp_state) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_int_latched) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_int_latched) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_int_level) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_lpf) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_lpf) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_set_sample_rate) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_sensors) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_set_sensors) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_write_mem) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_write_mem) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_sens) for mpu_get_gyro_sens
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(i.set_int_enable) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.set_int_enable) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for hw
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for test
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to memseta.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to memseta.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu.o(i.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.constdata) for .constdata
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mget_ms) for mget_ms
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    iic.o(i.IIC_Ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Ack) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_GPIO_Init) for IIC_GPIO_Init
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Start) for IIC_Start
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Stop) for IIC_Stop
    iic.o(i.IIC_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    iic.o(i.IIC_GPIO_Init) refers to iic.o(i.IIC_Stop) for IIC_Stop
    iic.o(i.IIC_NAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_NAck) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Read_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Read_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_Ack) for IIC_Ack
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_NAck) for IIC_NAck
    iic.o(i.IIC_Send_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Send_Byte) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Start) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Stop) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Wait_Ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Wait_Ack) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Wait_Ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    asin.o(i.__hardfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.__hardfp_asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.__hardfp_asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.__hardfp_asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.__hardfp_asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.__hardfp_asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.__hardfp_asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.__hardfp_asin) refers to fabs.o(i.fabs) for fabs
    asin.o(i.__hardfp_asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.__hardfp_asin) refers to asin.o(.constdata) for .constdata
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.____hardfp_asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.____hardfp_asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.____hardfp_asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.____hardfp_asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.____hardfp_asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.____hardfp_asin$lsc) refers to fabs.o(i.fabs) for fabs
    asin_x.o(i.____hardfp_asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.____hardfp_asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing tim.o(i.HAL_TIM_PWM_MspDeInit), (28 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (68 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (114 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing my_uart.o(.rev16_text), (4 bytes).
    Removing my_uart.o(.revsh_text), (4 bytes).
    Removing my_uart.o(.rrx_text), (6 bytes).
    Removing my_uart.o(i.my_printf), (96 bytes).
    Removing my_uart.o(i.uart_dma_is_busy), (12 bytes).
    Removing my_uart.o(i.uart_dma_wait_complete), (16 bytes).
    Removing gray.o(.rev16_text), (4 bytes).
    Removing gray.o(.revsh_text), (4 bytes).
    Removing gray.o(.rrx_text), (6 bytes).
    Removing motor_app.o(.rev16_text), (4 bytes).
    Removing motor_app.o(.revsh_text), (4 bytes).
    Removing motor_app.o(.rrx_text), (6 bytes).
    Removing motor_app.o(i.Motor_App_Backward), (32 bytes).
    Removing motor_app.o(i.Motor_App_Turn_Left), (32 bytes).
    Removing motor_app.o(i.Motor_App_Turn_Right), (32 bytes).
    Removing motor_driver.o(.rev16_text), (4 bytes).
    Removing motor_driver.o(.revsh_text), (4 bytes).
    Removing motor_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(i.Motor_Brake), (68 bytes).
    Removing mpu6050_app.o(.rev16_text), (4 bytes).
    Removing mpu6050_app.o(.revsh_text), (4 bytes).
    Removing mpu6050_app.o(.rrx_text), (6 bytes).
    Removing mpu6050_app.o(i.Set_Target_Direction), (16 bytes).
    Removing mpu6050.o(.rev16_text), (4 bytes).
    Removing mpu6050.o(.revsh_text), (4 bytes).
    Removing mpu6050.o(.rrx_text), (6 bytes).
    Removing mpu6050.o(i.MPU_Get_Accelerometer), (50 bytes).
    Removing mpu6050.o(i.MPU_Get_Gyroscope), (50 bytes).
    Removing mpu6050.o(i.MPU_Get_Temperature), (100 bytes).
    Removing pid.o(i.pid_app_limit_integral), (36 bytes).
    Removing pid.o(i.pid_calculate_incremental), (122 bytes).
    Removing pid.o(i.pid_calculate_positional), (102 bytes).
    Removing pid.o(i.pid_constrain), (32 bytes).
    Removing pid.o(i.pid_init), (48 bytes).
    Removing pid.o(i.pid_out_limit), (38 bytes).
    Removing pid.o(i.pid_reset), (40 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (6 bytes).
    Removing pid.o(i.pid_set_target), (6 bytes).
    Removing inv_mpu.o(.rev16_text), (4 bytes).
    Removing inv_mpu.o(.revsh_text), (4 bytes).
    Removing inv_mpu.o(.rrx_text), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_fsr), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_reg), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_dmp_state), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_get_int_status), (52 bytes).
    Removing inv_mpu.o(i.mpu_get_power_state), (20 bytes).
    Removing inv_mpu.o(i.mpu_get_temperature), (124 bytes).
    Removing inv_mpu.o(i.mpu_lp_motion_interrupt), (432 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo), (340 bytes).
    Removing inv_mpu.o(i.mpu_read_reg), (56 bytes).
    Removing inv_mpu.o(i.mpu_reg_dump), (64 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias), (220 bytes).
    Removing inv_mpu.o(i.mpu_set_compass_sample_rate), (6 bytes).
    Removing inv_mpu.o(i.mpu_set_int_level), (16 bytes).
    Removing inv_mpu.o(i.setup_compass), (6 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rev16_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.revsh_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rrx_text), (6 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count), (56 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time), (62 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode), (84 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time), (44 bytes).
    Removing iic.o(.rev16_text), (4 bytes).
    Removing iic.o(.revsh_text), (4 bytes).
    Removing iic.o(.rrx_text), (6 bytes).
    Removing iic.o(i.IIC_CheckDevice), (32 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dneg.o(.text), (6 bytes).

480 unused section(s) (total 31602 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\APP\Gray.c                            0x00000000   Number         0  gray.o ABSOLUTE
    ..\APP\MPU6050\IIC.c                     0x00000000   Number         0  iic.o ABSOLUTE
    ..\APP\MPU6050\inv_mpu.c                 0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\APP\MPU6050\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\APP\MPU6050\mpu6050.c                 0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\APP\PID\pid.c                         0x00000000   Number         0  pid.o ABSOLUTE
    ..\APP\key.c                             0x00000000   Number         0  key.o ABSOLUTE
    ..\APP\motor_app.c                       0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\APP\motor_driver.c                    0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\APP\mpu6050_app.c                     0x00000000   Number         0  mpu6050_app.o ABSOLUTE
    ..\APP\my_uart.c                         0x00000000   Number         0  my_uart.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\APP\\Gray.c                          0x00000000   Number         0  gray.o ABSOLUTE
    ..\\APP\\MPU6050\\IIC.c                  0x00000000   Number         0  iic.o ABSOLUTE
    ..\\APP\\MPU6050\\inv_mpu.c              0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\\APP\\MPU6050\\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\\APP\\MPU6050\\mpu6050.c              0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\\APP\\key.c                           0x00000000   Number         0  key.o ABSOLUTE
    ..\\APP\\motor_app.c                     0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\\APP\\motor_driver.c                  0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\\APP\\mpu6050_app.c                   0x00000000   Number         0  mpu6050_app.o ABSOLUTE
    ..\\APP\\my_uart.c                       0x00000000   Number         0  my_uart.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000226   Section        0  ldiv.o(.text)
    .text                                    0x08000288   Section        0  memcpya.o(.text)
    .text                                    0x080002ac   Section        0  memseta.o(.text)
    .text                                    0x080002d0   Section        0  memcmp.o(.text)
    .text                                    0x080002ea   Section        0  dadd.o(.text)
    .text                                    0x08000438   Section        0  dmul.o(.text)
    .text                                    0x0800051c   Section        0  ddiv.o(.text)
    .text                                    0x080005fa   Section        0  f2d.o(.text)
    .text                                    0x08000620   Section        0  d2f.o(.text)
    .text                                    0x08000658   Section        0  uidiv.o(.text)
    .text                                    0x08000684   Section        0  llshl.o(.text)
    .text                                    0x080006a2   Section        0  llushr.o(.text)
    .text                                    0x080006c2   Section        0  llsshr.o(.text)
    .text                                    0x080006e6   Section        0  fepilogue.o(.text)
    .text                                    0x080006e6   Section        0  iusefp.o(.text)
    .text                                    0x08000754   Section        0  depilogue.o(.text)
    .text                                    0x0800080e   Section        0  dfixul.o(.text)
    .text                                    0x08000840   Section       48  cdcmple.o(.text)
    .text                                    0x08000870   Section       48  cdrcmple.o(.text)
    .text                                    0x080008a0   Section       36  init.o(.text)
    .text                                    0x080008c4   Section        0  dsqrt.o(.text)
    i.BusFault_Handler                       0x08000966   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream2_IRQHandler                0x08000968   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA2_Stream7_IRQHandler                0x08000974   Section        0  stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08000980   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08000981   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x080009a8   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x080009a9   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x080009fc   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080009fd   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08000a24   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Direction_Control_Init                 0x08000a28   Section        0  mpu6050_app.o(i.Direction_Control_Init)
    i.Direction_Control_Update               0x08000a40   Section        0  mpu6050_app.o(i.Direction_Control_Update)
    i.Error_Handler                          0x08000a5c   Section        0  main.o(i.Error_Handler)
    i.Get_Direction_Correction               0x08000a60   Section        0  mpu6050_app.o(i.Get_Direction_Correction)
    i.Get_Yaw_Error                          0x08000aa4   Section        0  mpu6050_app.o(i.Get_Yaw_Error)
    i.Gray_Get_Active_Sensor_Count           0x08000af4   Section        0  gray.o(i.Gray_Get_Active_Sensor_Count)
    i.Gray_Get_Position_Error                0x08000b18   Section        0  gray.o(i.Gray_Get_Position_Error)
    i.Gray_Init                              0x08000b24   Section        0  gray.o(i.Gray_Init)
    i.Gray_Task                              0x08000b38   Section        0  gray.o(i.Gray_Task)
    i.Gray_get                               0x08000bb0   Section        0  gray.o(i.Gray_get)
    i.HAL_DMA_Abort                          0x08000c14   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000ca6   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000ccc   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000e6c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08000f40   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08000fb0   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000fd4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080011c4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080011ce   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080011d8   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x080011e4   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080011f4   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001228   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001268   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001298   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080012b4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080012f4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001318   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x0800144c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800146c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800148c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080014ec   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001858   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001880   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08001910   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x0800196c   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Encoder_Init                   0x08001994   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08001a38   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08001ae0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_MspPostInit                    0x08001b70   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08001bc0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08001c8c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08001ce8   Section        0  tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_Start                      0x08001d10   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_UARTEx_RxEventCallback             0x08001dd8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08001ddc   Section        0  my_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08001df4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002074   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080020d8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080021c0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x080021dc   Section        0  my_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit_DMA                  0x08002218   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    i.HAL_UART_TxCpltCallback                0x0800229c   Section        0  my_uart.o(i.HAL_UART_TxCpltCallback)
    i.HAL_UART_TxHalfCpltCallback            0x080022b4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    i.HardFault_Handler                      0x080022b6   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.IIC_Ack                                0x080022b8   Section        0  iic.o(i.IIC_Ack)
    i.IIC_Delay                              0x08002300   Section        0  iic.o(i.IIC_Delay)
    IIC_Delay                                0x08002301   Thumb Code    12  iic.o(i.IIC_Delay)
    i.IIC_GPIO_Init                          0x0800230c   Section        0  iic.o(i.IIC_GPIO_Init)
    i.IIC_NAck                               0x08002348   Section        0  iic.o(i.IIC_NAck)
    i.IIC_Read_Byte                          0x08002380   Section        0  iic.o(i.IIC_Read_Byte)
    i.IIC_Send_Byte                          0x080023e4   Section        0  iic.o(i.IIC_Send_Byte)
    i.IIC_Start                              0x0800244c   Section        0  iic.o(i.IIC_Start)
    i.IIC_Stop                               0x08002490   Section        0  iic.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x080024c4   Section        0  iic.o(i.IIC_Wait_Ack)
    i.Key_Read                               0x08002510   Section        0  key.o(i.Key_Read)
    i.MPU_Get_Gyro_Offset                    0x08002544   Section        0  mpu6050.o(i.MPU_Get_Gyro_Offset)
    i.MPU_Init                               0x08002576   Section        0  mpu6050.o(i.MPU_Init)
    i.MPU_Read_Byte                          0x080025ec   Section        0  mpu6050.o(i.MPU_Read_Byte)
    i.MPU_Read_Len                           0x08002628   Section        0  mpu6050.o(i.MPU_Read_Len)
    i.MPU_Set_Accel_Fsr                      0x08002694   Section        0  mpu6050.o(i.MPU_Set_Accel_Fsr)
    i.MPU_Set_Gyro_Fsr                       0x0800269e   Section        0  mpu6050.o(i.MPU_Set_Gyro_Fsr)
    i.MPU_Set_LPF                            0x080026a8   Section        0  mpu6050.o(i.MPU_Set_LPF)
    i.MPU_Set_Rate                           0x080026d8   Section        0  mpu6050.o(i.MPU_Set_Rate)
    i.MPU_Write_Byte                         0x08002706   Section        0  mpu6050.o(i.MPU_Write_Byte)
    i.MPU_Write_Len                          0x08002742   Section        0  mpu6050.o(i.MPU_Write_Len)
    i.MX_DMA_Init                            0x08002794   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x080027d0   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM14_Init                          0x080028f0   Section        0  tim.o(i.MX_TIM14_Init)
    i.MX_TIM1_Init                           0x08002924   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08002994   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08002a00   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_USART1_UART_Init                    0x08002a84   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08002abc   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_App_Forward                      0x08002ac0   Section        0  motor_app.o(i.Motor_App_Forward)
    i.Motor_App_Init                         0x08002ae4   Section        0  motor_app.o(i.Motor_App_Init)
    i.Motor_App_Set_Speed                    0x08002b34   Section        0  motor_app.o(i.Motor_App_Set_Speed)
    i.Motor_App_Stop                         0x08002b74   Section        0  motor_app.o(i.Motor_App_Stop)
    i.Motor_App_Task                         0x08002b8c   Section        0  motor_app.o(i.Motor_App_Task)
    i.Motor_Config_Init                      0x08002b8e   Section        0  motor_driver.o(i.Motor_Config_Init)
    i.Motor_Dead_Compensation                0x08002bfc   Section        0  motor_driver.o(i.Motor_Dead_Compensation)
    i.Motor_Limit_Speed                      0x08002c1a   Section        0  motor_driver.o(i.Motor_Limit_Speed)
    i.Motor_Set_Speed                        0x08002c2c   Section        0  motor_driver.o(i.Motor_Set_Speed)
    i.Motor_Stop                             0x08002cd0   Section        0  motor_driver.o(i.Motor_Stop)
    i.Mpu6050_Init                           0x08002d14   Section        0  mpu6050_app.o(i.Mpu6050_Init)
    i.Mpu6050_Task                           0x08002d5c   Section        0  mpu6050_app.o(i.Mpu6050_Task)
    i.NMI_Handler                            0x08002dac   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08002dae   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Reset_Target_Direction                 0x08002db0   Section        0  mpu6050_app.o(i.Reset_Target_Direction)
    i.SVC_Handler                            0x08002dc4   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08002dc6   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002dcc   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08002e60   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08002e70   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08002f40   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x08002f5c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08002f5d   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08002fbc   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08003028   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08003029   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08003090   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08003091   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.UART_DMAAbortOnError                   0x080030e0   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080030e1   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080030ee   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080030ef   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMATransmitCplt                   0x08003138   Section        0  stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt)
    UART_DMATransmitCplt                     0x08003139   Thumb Code    66  stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt)
    i.UART_DMATxHalfCplt                     0x0800317a   Section        0  stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt)
    UART_DMATxHalfCplt                       0x0800317b   Thumb Code    10  stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt)
    i.UART_EndRxTransfer                     0x08003184   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08003185   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x080031d2   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080031d3   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x080031ee   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080031ef   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080032b0   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080032b1   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x080033bc   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.USART1_IRQHandler                      0x080033f4   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08003400   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x08003404   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassify                       0x08003438   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08003468   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08003469   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_asin                          0x08003488   Section        0  asin.o(i.__hardfp_asin)
    i.__hardfp_atan                          0x080037e8   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x08003ac0   Section        0  atan2.o(i.__hardfp_atan2)
    i.__kernel_poly                          0x08003cc0   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x08003db8   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08003dcc   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08003de0   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08003e00   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08003e20   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08003e2e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003e30   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08003e40   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08003e4c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08003e4d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08003fd0   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08003fd1   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08004684   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08004685   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080046a8   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080046a9   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x080046d6   Section        0  printfa.o(i._snputc)
    _snputc                                  0x080046d7   Thumb Code    22  printfa.o(i._snputc)
    i.accel_self_test                        0x080046ec   Section        0  inv_mpu.o(i.accel_self_test)
    accel_self_test                          0x080046ed   Thumb Code   126  inv_mpu.o(i.accel_self_test)
    i.atan                                   0x0800477c   Section        0  atan.o(i.atan)
    i.convert_to_continuous_yaw              0x0800478c   Section        0  mpu6050_app.o(i.convert_to_continuous_yaw)
    i.dmp_enable_6x_lp_quat                  0x080047ec   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    i.dmp_enable_feature                     0x08004828   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    i.dmp_enable_gyro_cal                    0x08004a00   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    i.dmp_enable_lp_quat                     0x08004a3c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    i.dmp_load_motion_driver_firmware        0x08004a78   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    i.dmp_read_fifo                          0x08004a8c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    i.dmp_set_accel_bias                     0x08004bf4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    i.dmp_set_fifo_rate                      0x08004ccc   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    i.dmp_set_gyro_bias                      0x08004d30   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    i.dmp_set_orientation                    0x08004dfc   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    i.dmp_set_shake_reject_thresh            0x08004f08   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    i.dmp_set_shake_reject_time              0x08004f38   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    i.dmp_set_shake_reject_timeout           0x08004f58   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    i.dmp_set_tap_axes                       0x08004f78   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    i.dmp_set_tap_count                      0x08004fb8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    i.dmp_set_tap_thresh                     0x08004fdc   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    i.dmp_set_tap_time                       0x0800513c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    i.dmp_set_tap_time_multi                 0x0800515c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    i.fabs                                   0x0800517c   Section        0  fabs.o(i.fabs)
    i.get_accel_prod_shift                   0x08005194   Section        0  inv_mpu.o(i.get_accel_prod_shift)
    get_accel_prod_shift                     0x08005195   Thumb Code   164  inv_mpu.o(i.get_accel_prod_shift)
    i.get_st_biases                          0x08005248   Section        0  inv_mpu.o(i.get_st_biases)
    get_st_biases                            0x08005249   Thumb Code   784  inv_mpu.o(i.get_st_biases)
    i.gray_task                              0x0800555c   Section        0  scheduler.o(i.gray_task)
    i.gyro_self_test                         0x0800570c   Section        0  inv_mpu.o(i.gyro_self_test)
    gyro_self_test                           0x0800570d   Thumb Code   190  inv_mpu.o(i.gyro_self_test)
    i.inv_orientation_matrix_to_scalar       0x080057e0   Section        0  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    i.inv_row_2_scale                        0x08005802   Section        0  inv_mpu.o(i.inv_row_2_scale)
    i.key_task                               0x0800584c   Section        0  key.o(i.key_task)
    i.main                                   0x08005898   Section        0  main.o(i.main)
    i.mget_ms                                0x08005924   Section        0  inv_mpu.o(i.mget_ms)
    i.motor_task                             0x08005926   Section        0  scheduler.o(i.motor_task)
    i.mpu_configure_fifo                     0x08005938   Section        0  inv_mpu.o(i.mpu_configure_fifo)
    i.mpu_dmp_get_data                       0x0800598c   Section        0  inv_mpu.o(i.mpu_dmp_get_data)
    i.mpu_dmp_init                           0x08005af0   Section        0  inv_mpu.o(i.mpu_dmp_init)
    i.mpu_get_accel_fsr                      0x08005b9c   Section        0  inv_mpu.o(i.mpu_get_accel_fsr)
    i.mpu_get_accel_sens                     0x08005bd8   Section        0  inv_mpu.o(i.mpu_get_accel_sens)
    i.mpu_get_gyro_fsr                       0x08005c1c   Section        0  inv_mpu.o(i.mpu_get_gyro_fsr)
    i.mpu_get_gyro_sens                      0x08005c50   Section        0  inv_mpu.o(i.mpu_get_gyro_sens)
    i.mpu_get_lpf                            0x08005c9c   Section        0  inv_mpu.o(i.mpu_get_lpf)
    i.mpu_get_sample_rate                    0x08005cd4   Section        0  inv_mpu.o(i.mpu_get_sample_rate)
    i.mpu_init                               0x08005cf0   Section        0  inv_mpu.o(i.mpu_init)
    i.mpu_load_firmware                      0x08005e14   Section        0  inv_mpu.o(i.mpu_load_firmware)
    i.mpu_lp_accel_mode                      0x08005eb4   Section        0  inv_mpu.o(i.mpu_lp_accel_mode)
    i.mpu_read_fifo_stream                   0x08005f6c   Section        0  inv_mpu.o(i.mpu_read_fifo_stream)
    i.mpu_read_mem                           0x08005ffc   Section        0  inv_mpu.o(i.mpu_read_mem)
    i.mpu_reset_fifo                         0x08006050   Section        0  inv_mpu.o(i.mpu_reset_fifo)
    i.mpu_run_self_test                      0x080061b0   Section        0  inv_mpu.o(i.mpu_run_self_test)
    i.mpu_set_accel_fsr                      0x080062a0   Section        0  inv_mpu.o(i.mpu_set_accel_fsr)
    i.mpu_set_bypass                         0x08006308   Section        0  inv_mpu.o(i.mpu_set_bypass)
    i.mpu_set_dmp_state                      0x080063e4   Section        0  inv_mpu.o(i.mpu_set_dmp_state)
    i.mpu_set_gyro_fsr                       0x0800645c   Section        0  inv_mpu.o(i.mpu_set_gyro_fsr)
    i.mpu_set_int_latched                    0x080064c8   Section        0  inv_mpu.o(i.mpu_set_int_latched)
    i.mpu_set_lpf                            0x0800652c   Section        0  inv_mpu.o(i.mpu_set_lpf)
    i.mpu_set_sample_rate                    0x08006598   Section        0  inv_mpu.o(i.mpu_set_sample_rate)
    i.mpu_set_sensors                        0x08006618   Section        0  inv_mpu.o(i.mpu_set_sensors)
    i.mpu_task                               0x080066d0   Section        0  scheduler.o(i.mpu_task)
    i.mpu_write_mem                          0x080066d4   Section        0  inv_mpu.o(i.mpu_write_mem)
    i.my_printf_nb                           0x08006728   Section        0  my_uart.o(i.my_printf_nb)
    i.run_self_test                          0x08006780   Section        0  inv_mpu.o(i.run_self_test)
    i.scheduler_init                         0x08006808   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08006820   Section        0  scheduler.o(i.scheduler_run)
    i.set_int_enable                         0x08006860   Section        0  inv_mpu.o(i.set_int_enable)
    set_int_enable                           0x08006861   Thumb Code    84  inv_mpu.o(i.set_int_enable)
    i.sqrt                                   0x080068b8   Section        0  sqrt.o(i.sqrt)
    i.uart_task                              0x08006928   Section        0  scheduler.o(i.uart_task)
    .constdata                               0x08006a5c   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08006a5c   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08006a64   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08006a74   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08006a7c   Section       27  inv_mpu.o(.constdata)
    .constdata                               0x08006a98   Section       12  inv_mpu.o(.constdata)
    .constdata                               0x08006aa4   Section       40  inv_mpu.o(.constdata)
    .constdata                               0x08006acc   Section     3062  inv_mpu_dmp_motion_driver.o(.constdata)
    dmp_memory                               0x08006acc   Data        3062  inv_mpu_dmp_motion_driver.o(.constdata)
    .constdata                               0x080076c8   Section       80  asin.o(.constdata)
    pS                                       0x080076c8   Data          48  asin.o(.constdata)
    qS                                       0x080076f8   Data          32  asin.o(.constdata)
    .constdata                               0x08007718   Section      152  atan.o(.constdata)
    atanhi                                   0x08007718   Data          32  atan.o(.constdata)
    atanlo                                   0x08007738   Data          32  atan.o(.constdata)
    aTodd                                    0x08007758   Data          40  atan.o(.constdata)
    aTeven                                   0x08007780   Data          48  atan.o(.constdata)
    .constdata                               0x080077b0   Section        8  qnan.o(.constdata)
    .conststring                             0x080077b8   Section      104  scheduler.o(.conststring)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       80  scheduler.o(.data)
    output_count                             0x20000010   Data           1  scheduler.o(.data)
    last_line_detected                       0x20000011   Data           1  scheduler.o(.data)
    beep_active                              0x20000012   Data           1  scheduler.o(.data)
    line_stopped                             0x20000013   Data           1  scheduler.o(.data)
    last_mode                                0x20000014   Data           1  scheduler.o(.data)
    lost_line_count                          0x20000015   Data           1  scheduler.o(.data)
    mode_init                                0x20000016   Data           1  scheduler.o(.data)
    beep_start_time                          0x20000018   Data           4  scheduler.o(.data)
    scheduler_task                           0x20000024   Data          60  scheduler.o(.data)
    .data                                    0x20000060   Section        7  key.o(.data)
    .data                                    0x20000068   Section        8  my_uart.o(.data)
    uart_tx_busy                             0x20000068   Data           1  my_uart.o(.data)
    .data                                    0x20000070   Section       40  gray.o(.data)
    .data                                    0x20000098   Section       36  mpu6050_app.o(.data)
    direction_initialized                    0x20000098   Data           1  mpu6050_app.o(.data)
    is_initialized                           0x20000099   Data           1  mpu6050_app.o(.data)
    target_yaw                               0x2000009c   Data           4  mpu6050_app.o(.data)
    error_count                              0x200000a0   Data           4  mpu6050_app.o(.data)
    success_count                            0x200000a4   Data           4  mpu6050_app.o(.data)
    last_yaw                                 0x200000a8   Data           4  mpu6050_app.o(.data)
    revolution_count                         0x200000ac   Data           4  mpu6050_app.o(.data)
    .data                                    0x200000bc   Section       53  inv_mpu.o(.data)
    st                                       0x200000bc   Data          44  inv_mpu.o(.data)
    gyro_orientation                         0x200000e8   Data           9  inv_mpu.o(.data)
    .data                                    0x200000f4   Section        4  errno.o(.data)
    _errno                                   0x200000f4   Data           4  errno.o(.data)
    .bss                                     0x200000f8   Section      288  tim.o(.bss)
    .bss                                     0x20000218   Section      264  usart.o(.bss)
    .bss                                     0x20000320   Section      640  my_uart.o(.bss)
    uart_tx_buffer                           0x200003a0   Data         512  my_uart.o(.bss)
    .bss                                     0x200005a0   Section       72  motor_app.o(.bss)
    .bss                                     0x200005e8   Section       16  inv_mpu_dmp_motion_driver.o(.bss)
    dmp                                      0x200005e8   Data          16  inv_mpu_dmp_motion_driver.o(.bss)
    STACK                                    0x200005f8   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_ldivmod                          0x08000227   Thumb Code    98  ldiv.o(.text)
    __aeabi_memcpy                           0x08000289   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000289   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000289   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x080002ad   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080002ad   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080002ad   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080002bb   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080002bb   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080002bb   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080002bf   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x080002d1   Thumb Code    26  memcmp.o(.text)
    __aeabi_dadd                             0x080002eb   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x0800042d   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000433   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000439   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x0800051d   Thumb Code   222  ddiv.o(.text)
    __aeabi_f2d                              0x080005fb   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000621   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000659   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000659   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x08000685   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000685   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080006a3   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080006a3   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080006c3   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006c3   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x080006e7   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080006e7   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080006f9   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000755   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000773   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x0800080f   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdcmpeq                          0x08000841   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000841   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x08000871   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080008a1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080008a1   Thumb Code     0  init.o(.text)
    _dsqrt                                   0x080008c5   Thumb Code   162  dsqrt.o(.text)
    BusFault_Handler                         0x08000967   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream2_IRQHandler                  0x08000969   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DMA2_Stream7_IRQHandler                  0x08000975   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler)
    DebugMon_Handler                         0x08000a25   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Direction_Control_Init                   0x08000a29   Thumb Code    16  mpu6050_app.o(i.Direction_Control_Init)
    Direction_Control_Update                 0x08000a41   Thumb Code    22  mpu6050_app.o(i.Direction_Control_Update)
    Error_Handler                            0x08000a5d   Thumb Code     4  main.o(i.Error_Handler)
    Get_Direction_Correction                 0x08000a61   Thumb Code    62  mpu6050_app.o(i.Get_Direction_Correction)
    Get_Yaw_Error                            0x08000aa5   Thumb Code    58  mpu6050_app.o(i.Get_Yaw_Error)
    Gray_Get_Active_Sensor_Count             0x08000af5   Thumb Code    30  gray.o(i.Gray_Get_Active_Sensor_Count)
    Gray_Get_Position_Error                  0x08000b19   Thumb Code     8  gray.o(i.Gray_Get_Position_Error)
    Gray_Init                                0x08000b25   Thumb Code    12  gray.o(i.Gray_Init)
    Gray_Task                                0x08000b39   Thumb Code   110  gray.o(i.Gray_Task)
    Gray_get                                 0x08000bb1   Thumb Code    90  gray.o(i.Gray_get)
    HAL_DMA_Abort                            0x08000c15   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000ca7   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000ccd   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000e6d   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08000f41   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08000fb1   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000fd5   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080011c5   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080011cf   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080011d9   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x080011e5   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080011f5   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001229   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001269   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001299   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080012b5   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080012f5   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001319   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x0800144d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800146d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800148d   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080014ed   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001859   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08001881   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001911   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x0800196d   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Encoder_Init                     0x08001995   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08001a39   Thumb Code   152  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08001ae1   Thumb Code   142  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_MspPostInit                      0x08001b71   Thumb Code    68  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_PWM_ConfigChannel                0x08001bc1   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08001c8d   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08001ce9   Thumb Code    30  tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_Start                        0x08001d11   Thumb Code   172  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_UARTEx_RxEventCallback               0x08001dd9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08001ddd   Thumb Code    16  my_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08001df5   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002075   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080020d9   Thumb Code   210  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080021c1   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x080021dd   Thumb Code    38  my_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit_DMA                    0x08002219   Thumb Code   120  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    HAL_UART_TxCpltCallback                  0x0800229d   Thumb Code    16  my_uart.o(i.HAL_UART_TxCpltCallback)
    HAL_UART_TxHalfCpltCallback              0x080022b5   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    HardFault_Handler                        0x080022b7   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    IIC_Ack                                  0x080022b9   Thumb Code    66  iic.o(i.IIC_Ack)
    IIC_GPIO_Init                            0x0800230d   Thumb Code    52  iic.o(i.IIC_GPIO_Init)
    IIC_NAck                                 0x08002349   Thumb Code    52  iic.o(i.IIC_NAck)
    IIC_Read_Byte                            0x08002381   Thumb Code    96  iic.o(i.IIC_Read_Byte)
    IIC_Send_Byte                            0x080023e5   Thumb Code    98  iic.o(i.IIC_Send_Byte)
    IIC_Start                                0x0800244d   Thumb Code    64  iic.o(i.IIC_Start)
    IIC_Stop                                 0x08002491   Thumb Code    46  iic.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x080024c5   Thumb Code    70  iic.o(i.IIC_Wait_Ack)
    Key_Read                                 0x08002511   Thumb Code    46  key.o(i.Key_Read)
    MPU_Get_Gyro_Offset                      0x08002545   Thumb Code    50  mpu6050.o(i.MPU_Get_Gyro_Offset)
    MPU_Init                                 0x08002577   Thumb Code   118  mpu6050.o(i.MPU_Init)
    MPU_Read_Byte                            0x080025ed   Thumb Code    58  mpu6050.o(i.MPU_Read_Byte)
    MPU_Read_Len                             0x08002629   Thumb Code   108  mpu6050.o(i.MPU_Read_Len)
    MPU_Set_Accel_Fsr                        0x08002695   Thumb Code    10  mpu6050.o(i.MPU_Set_Accel_Fsr)
    MPU_Set_Gyro_Fsr                         0x0800269f   Thumb Code    10  mpu6050.o(i.MPU_Set_Gyro_Fsr)
    MPU_Set_LPF                              0x080026a9   Thumb Code    48  mpu6050.o(i.MPU_Set_LPF)
    MPU_Set_Rate                             0x080026d9   Thumb Code    46  mpu6050.o(i.MPU_Set_Rate)
    MPU_Write_Byte                           0x08002707   Thumb Code    60  mpu6050.o(i.MPU_Write_Byte)
    MPU_Write_Len                            0x08002743   Thumb Code    82  mpu6050.o(i.MPU_Write_Len)
    MX_DMA_Init                              0x08002795   Thumb Code    56  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x080027d1   Thumb Code   268  gpio.o(i.MX_GPIO_Init)
    MX_TIM14_Init                            0x080028f1   Thumb Code    42  tim.o(i.MX_TIM14_Init)
    MX_TIM1_Init                             0x08002925   Thumb Code   102  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08002995   Thumb Code   102  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08002a01   Thumb Code   122  tim.o(i.MX_TIM3_Init)
    MX_USART1_UART_Init                      0x08002a85   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08002abd   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_App_Forward                        0x08002ac1   Thumb Code    32  motor_app.o(i.Motor_App_Forward)
    Motor_App_Init                           0x08002ae5   Thumb Code    62  motor_app.o(i.Motor_App_Init)
    Motor_App_Set_Speed                      0x08002b35   Thumb Code    58  motor_app.o(i.Motor_App_Set_Speed)
    Motor_App_Stop                           0x08002b75   Thumb Code    20  motor_app.o(i.Motor_App_Stop)
    Motor_App_Task                           0x08002b8d   Thumb Code     2  motor_app.o(i.Motor_App_Task)
    Motor_Config_Init                        0x08002b8f   Thumb Code   110  motor_driver.o(i.Motor_Config_Init)
    Motor_Dead_Compensation                  0x08002bfd   Thumb Code    30  motor_driver.o(i.Motor_Dead_Compensation)
    Motor_Limit_Speed                        0x08002c1b   Thumb Code    18  motor_driver.o(i.Motor_Limit_Speed)
    Motor_Set_Speed                          0x08002c2d   Thumb Code   164  motor_driver.o(i.Motor_Set_Speed)
    Motor_Stop                               0x08002cd1   Thumb Code    68  motor_driver.o(i.Motor_Stop)
    Mpu6050_Init                             0x08002d15   Thumb Code    64  mpu6050_app.o(i.Mpu6050_Init)
    Mpu6050_Task                             0x08002d5d   Thumb Code    76  mpu6050_app.o(i.Mpu6050_Task)
    NMI_Handler                              0x08002dad   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08002daf   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Reset_Target_Direction                   0x08002db1   Thumb Code    16  mpu6050_app.o(i.Reset_Target_Direction)
    SVC_Handler                              0x08002dc5   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08002dc7   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002dcd   Thumb Code   140  main.o(i.SystemClock_Config)
    SystemInit                               0x08002e61   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08002e71   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08002f41   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x08002fbd   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_IT                    0x080033bd   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x080033f5   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08003401   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x08003405   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08003405   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08003405   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08003405   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08003405   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassify                         0x08003439   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_asin                            0x08003489   Thumb Code   770  asin.o(i.__hardfp_asin)
    __hardfp_atan                            0x080037e9   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x08003ac1   Thumb Code   448  atan2.o(i.__hardfp_atan2)
    __kernel_poly                            0x08003cc1   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x08003db9   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08003dcd   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08003de1   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08003e01   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08003e21   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08003e2f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003e31   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08003e41   Thumb Code     6  errno.o(i.__set_errno)
    atan                                     0x0800477d   Thumb Code    16  atan.o(i.atan)
    convert_to_continuous_yaw                0x0800478d   Thumb Code    78  mpu6050_app.o(i.convert_to_continuous_yaw)
    dmp_enable_6x_lp_quat                    0x080047ed   Thumb Code    58  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    dmp_enable_feature                       0x08004829   Thumb Code   464  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    dmp_enable_gyro_cal                      0x08004a01   Thumb Code    34  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    dmp_enable_lp_quat                       0x08004a3d   Thumb Code    58  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    dmp_load_motion_driver_firmware          0x08004a79   Thumb Code    16  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    dmp_read_fifo                            0x08004a8d   Thumb Code   356  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    dmp_set_accel_bias                       0x08004bf5   Thumb Code   210  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    dmp_set_fifo_rate                        0x08004ccd   Thumb Code    82  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    dmp_set_gyro_bias                        0x08004d31   Thumb Code   196  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    dmp_set_orientation                      0x08004dfd   Thumb Code   246  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    dmp_set_shake_reject_thresh              0x08004f09   Thumb Code    48  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    dmp_set_shake_reject_time                0x08004f39   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    dmp_set_shake_reject_timeout             0x08004f59   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    dmp_set_tap_axes                         0x08004f79   Thumb Code    64  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    dmp_set_tap_count                        0x08004fb9   Thumb Code    34  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    dmp_set_tap_thresh                       0x08004fdd   Thumb Code   314  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    dmp_set_tap_time                         0x0800513d   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    dmp_set_tap_time_multi                   0x0800515d   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    fabs                                     0x0800517d   Thumb Code    24  fabs.o(i.fabs)
    gray_task                                0x0800555d   Thumb Code   424  scheduler.o(i.gray_task)
    inv_orientation_matrix_to_scalar         0x080057e1   Thumb Code    34  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    inv_row_2_scale                          0x08005803   Thumb Code    72  inv_mpu.o(i.inv_row_2_scale)
    key_task                                 0x0800584d   Thumb Code    72  key.o(i.key_task)
    main                                     0x08005899   Thumb Code   110  main.o(i.main)
    mget_ms                                  0x08005925   Thumb Code     2  inv_mpu.o(i.mget_ms)
    motor_task                               0x08005927   Thumb Code    16  scheduler.o(i.motor_task)
    mpu_configure_fifo                       0x08005939   Thumb Code    78  inv_mpu.o(i.mpu_configure_fifo)
    mpu_dmp_get_data                         0x0800598d   Thumb Code   344  inv_mpu.o(i.mpu_dmp_get_data)
    mpu_dmp_init                             0x08005af1   Thumb Code   168  inv_mpu.o(i.mpu_dmp_init)
    mpu_get_accel_fsr                        0x08005b9d   Thumb Code    54  inv_mpu.o(i.mpu_get_accel_fsr)
    mpu_get_accel_sens                       0x08005bd9   Thumb Code    62  inv_mpu.o(i.mpu_get_accel_sens)
    mpu_get_gyro_fsr                         0x08005c1d   Thumb Code    48  inv_mpu.o(i.mpu_get_gyro_fsr)
    mpu_get_gyro_sens                        0x08005c51   Thumb Code    54  inv_mpu.o(i.mpu_get_gyro_sens)
    mpu_get_lpf                              0x08005c9d   Thumb Code    52  inv_mpu.o(i.mpu_get_lpf)
    mpu_get_sample_rate                      0x08005cd5   Thumb Code    22  inv_mpu.o(i.mpu_get_sample_rate)
    mpu_init                                 0x08005cf1   Thumb Code   288  inv_mpu.o(i.mpu_init)
    mpu_load_firmware                        0x08005e15   Thumb Code   154  inv_mpu.o(i.mpu_load_firmware)
    mpu_lp_accel_mode                        0x08005eb5   Thumb Code   178  inv_mpu.o(i.mpu_lp_accel_mode)
    mpu_read_fifo_stream                     0x08005f6d   Thumb Code   140  inv_mpu.o(i.mpu_read_fifo_stream)
    mpu_read_mem                             0x08005ffd   Thumb Code    80  inv_mpu.o(i.mpu_read_mem)
    mpu_reset_fifo                           0x08006051   Thumb Code   346  inv_mpu.o(i.mpu_reset_fifo)
    mpu_run_self_test                        0x080061b1   Thumb Code   234  inv_mpu.o(i.mpu_run_self_test)
    mpu_set_accel_fsr                        0x080062a1   Thumb Code    98  inv_mpu.o(i.mpu_set_accel_fsr)
    mpu_set_bypass                           0x08006309   Thumb Code   214  inv_mpu.o(i.mpu_set_bypass)
    mpu_set_dmp_state                        0x080063e5   Thumb Code   114  inv_mpu.o(i.mpu_set_dmp_state)
    mpu_set_gyro_fsr                         0x0800645d   Thumb Code   104  inv_mpu.o(i.mpu_set_gyro_fsr)
    mpu_set_int_latched                      0x080064c9   Thumb Code    94  inv_mpu.o(i.mpu_set_int_latched)
    mpu_set_lpf                              0x0800652d   Thumb Code   102  inv_mpu.o(i.mpu_set_lpf)
    mpu_set_sample_rate                      0x08006599   Thumb Code   124  inv_mpu.o(i.mpu_set_sample_rate)
    mpu_set_sensors                          0x08006619   Thumb Code   178  inv_mpu.o(i.mpu_set_sensors)
    mpu_task                                 0x080066d1   Thumb Code     4  scheduler.o(i.mpu_task)
    mpu_write_mem                            0x080066d5   Thumb Code    80  inv_mpu.o(i.mpu_write_mem)
    my_printf_nb                             0x08006729   Thumb Code    80  my_uart.o(i.my_printf_nb)
    run_self_test                            0x08006781   Thumb Code   136  inv_mpu.o(i.run_self_test)
    scheduler_init                           0x08006809   Thumb Code    20  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08006821   Thumb Code    60  scheduler.o(i.scheduler_run)
    sqrt                                     0x080068b9   Thumb Code   110  sqrt.o(i.sqrt)
    uart_task                                0x08006929   Thumb Code   238  scheduler.o(i.uart_task)
    AHBPrescTable                            0x08006a64   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08006a74   Data           8  system_stm32f4xx.o(.constdata)
    reg                                      0x08006a7c   Data          27  inv_mpu.o(.constdata)
    hw                                       0x08006a98   Data          12  inv_mpu.o(.constdata)
    test                                     0x08006aa4   Data          40  inv_mpu.o(.constdata)
    __mathlib_zero                           0x080077b0   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08007820   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007840   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    task_num                                 0x20000017   Data           1  scheduler.o(.data)
    gray_buff                                0x2000001c   Data           8  scheduler.o(.data)
    sys_mode                                 0x20000060   Data           1  key.o(.data)
    sys_ture                                 0x20000061   Data           1  key.o(.data)
    pre_mode                                 0x20000062   Data           1  key.o(.data)
    key_val                                  0x20000063   Data           1  key.o(.data)
    key_old                                  0x20000064   Data           1  key.o(.data)
    key_down                                 0x20000065   Data           1  key.o(.data)
    key_up                                   0x20000066   Data           1  key.o(.data)
    uart_rx_index                            0x2000006a   Data           2  my_uart.o(.data)
    uart_rx_ticks                            0x2000006c   Data           4  my_uart.o(.data)
    gray_digital_data                        0x20000070   Data           1  gray.o(.data)
    g_line_position_error                    0x20000074   Data           4  gray.o(.data)
    gray_weights                             0x20000078   Data          32  gray.o(.data)
    Pitch                                    0x200000b0   Data           4  mpu6050_app.o(.data)
    Roll                                     0x200000b4   Data           4  mpu6050_app.o(.data)
    Yaw                                      0x200000b8   Data           4  mpu6050_app.o(.data)
    htim1                                    0x200000f8   Data          72  tim.o(.bss)
    htim2                                    0x20000140   Data          72  tim.o(.bss)
    htim3                                    0x20000188   Data          72  tim.o(.bss)
    htim14                                   0x200001d0   Data          72  tim.o(.bss)
    huart1                                   0x20000218   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x20000260   Data          96  usart.o(.bss)
    hdma_usart1_tx                           0x200002c0   Data          96  usart.o(.bss)
    uart_rx_buffer                           0x20000320   Data         128  my_uart.o(.bss)
    left_motor                               0x200005a0   Data          36  motor_app.o(.bss)
    right_motor                              0x200005c4   Data          36  motor_app.o(.bss)
    __initial_sp                             0x200009f8   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007938, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007840, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         4314  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         4420    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         4423    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4425    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4427    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         4428    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         4435    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         4430    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         4432    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         4421    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO         4317    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x00000062   Code   RO         4319    .text               mc_w.l(ldiv.o)
    0x08000288   0x08000288   0x00000024   Code   RO         4321    .text               mc_w.l(memcpya.o)
    0x080002ac   0x080002ac   0x00000024   Code   RO         4323    .text               mc_w.l(memseta.o)
    0x080002d0   0x080002d0   0x0000001a   Code   RO         4325    .text               mc_w.l(memcmp.o)
    0x080002ea   0x080002ea   0x0000014e   Code   RO         4357    .text               mf_w.l(dadd.o)
    0x08000438   0x08000438   0x000000e4   Code   RO         4359    .text               mf_w.l(dmul.o)
    0x0800051c   0x0800051c   0x000000de   Code   RO         4361    .text               mf_w.l(ddiv.o)
    0x080005fa   0x080005fa   0x00000026   Code   RO         4365    .text               mf_w.l(f2d.o)
    0x08000620   0x08000620   0x00000038   Code   RO         4367    .text               mf_w.l(d2f.o)
    0x08000658   0x08000658   0x0000002c   Code   RO         4439    .text               mc_w.l(uidiv.o)
    0x08000684   0x08000684   0x0000001e   Code   RO         4441    .text               mc_w.l(llshl.o)
    0x080006a2   0x080006a2   0x00000020   Code   RO         4443    .text               mc_w.l(llushr.o)
    0x080006c2   0x080006c2   0x00000024   Code   RO         4445    .text               mc_w.l(llsshr.o)
    0x080006e6   0x080006e6   0x00000000   Code   RO         4454    .text               mc_w.l(iusefp.o)
    0x080006e6   0x080006e6   0x0000006e   Code   RO         4455    .text               mf_w.l(fepilogue.o)
    0x08000754   0x08000754   0x000000ba   Code   RO         4457    .text               mf_w.l(depilogue.o)
    0x0800080e   0x0800080e   0x00000030   Code   RO         4461    .text               mf_w.l(dfixul.o)
    0x0800083e   0x0800083e   0x00000002   PAD
    0x08000840   0x08000840   0x00000030   Code   RO         4463    .text               mf_w.l(cdcmple.o)
    0x08000870   0x08000870   0x00000030   Code   RO         4465    .text               mf_w.l(cdrcmple.o)
    0x080008a0   0x080008a0   0x00000024   Code   RO         4467    .text               mc_w.l(init.o)
    0x080008c4   0x080008c4   0x000000a2   Code   RO         4471    .text               mf_w.l(dsqrt.o)
    0x08000966   0x08000966   0x00000002   Code   RO          382    i.BusFault_Handler  stm32f4xx_it.o
    0x08000968   0x08000968   0x0000000c   Code   RO          383    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000974   0x08000974   0x0000000c   Code   RO          384    i.DMA2_Stream7_IRQHandler  stm32f4xx_it.o
    0x08000980   0x08000980   0x00000028   Code   RO         1932    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x080009a8   0x080009a8   0x00000054   Code   RO         1933    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x080009fc   0x080009fc   0x00000028   Code   RO         1934    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08000a24   0x08000a24   0x00000002   Code   RO          385    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000a26   0x08000a26   0x00000002   PAD
    0x08000a28   0x08000a28   0x00000018   Code   RO         3420    i.Direction_Control_Init  mpu6050_app.o
    0x08000a40   0x08000a40   0x0000001c   Code   RO         3421    i.Direction_Control_Update  mpu6050_app.o
    0x08000a5c   0x08000a5c   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000a60   0x08000a60   0x00000044   Code   RO         3422    i.Get_Direction_Correction  mpu6050_app.o
    0x08000aa4   0x08000aa4   0x00000050   Code   RO         3423    i.Get_Yaw_Error     mpu6050_app.o
    0x08000af4   0x08000af4   0x00000024   Code   RO         3235    i.Gray_Get_Active_Sensor_Count  gray.o
    0x08000b18   0x08000b18   0x0000000c   Code   RO         3236    i.Gray_Get_Position_Error  gray.o
    0x08000b24   0x08000b24   0x00000014   Code   RO         3237    i.Gray_Init         gray.o
    0x08000b38   0x08000b38   0x00000078   Code   RO         3238    i.Gray_Task         gray.o
    0x08000bb0   0x08000bb0   0x00000064   Code   RO         3239    i.Gray_get          gray.o
    0x08000c14   0x08000c14   0x00000092   Code   RO         1935    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08000ca6   0x08000ca6   0x00000024   Code   RO         1936    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08000cca   0x08000cca   0x00000002   PAD
    0x08000ccc   0x08000ccc   0x000001a0   Code   RO         1940    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08000e6c   0x08000e6c   0x000000d4   Code   RO         1941    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08000f40   0x08000f40   0x0000006e   Code   RO         1945    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08000fae   0x08000fae   0x00000002   PAD
    0x08000fb0   0x08000fb0   0x00000024   Code   RO         2372    i.HAL_Delay         stm32f4xx_hal.o
    0x08000fd4   0x08000fd4   0x000001f0   Code   RO         1828    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x080011c4   0x080011c4   0x0000000a   Code   RO         1830    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x080011ce   0x080011ce   0x0000000a   Code   RO         1832    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080011d8   0x080011d8   0x0000000c   Code   RO         2378    i.HAL_GetTick       stm32f4xx_hal.o
    0x080011e4   0x080011e4   0x00000010   Code   RO         2384    i.HAL_IncTick       stm32f4xx_hal.o
    0x080011f4   0x080011f4   0x00000034   Code   RO         2385    i.HAL_Init          stm32f4xx_hal.o
    0x08001228   0x08001228   0x00000040   Code   RO         2386    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001268   0x08001268   0x00000030   Code   RO          476    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001298   0x08001298   0x0000001a   Code   RO         2220    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x080012b2   0x080012b2   0x00000002   PAD
    0x080012b4   0x080012b4   0x00000040   Code   RO         2226    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080012f4   0x080012f4   0x00000024   Code   RO         2227    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001318   0x08001318   0x00000134   Code   RO         1474    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x0800144c   0x0800144c   0x00000020   Code   RO         1481    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800146c   0x0800146c   0x00000020   Code   RO         1482    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800148c   0x0800148c   0x00000060   Code   RO         1483    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080014ec   0x080014ec   0x0000036c   Code   RO         1486    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001858   0x08001858   0x00000028   Code   RO         2231    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001880   0x08001880   0x00000090   Code   RO         1225    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08001910   0x08001910   0x0000005a   Code   RO          502    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800196a   0x0800196a   0x00000002   PAD
    0x0800196c   0x0800196c   0x00000028   Code   RO          251    i.HAL_TIM_Base_MspInit  tim.o
    0x08001994   0x08001994   0x000000a4   Code   RO          523    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08001a38   0x08001a38   0x000000a8   Code   RO          253    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08001ae0   0x08001ae0   0x0000008e   Code   RO          526    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x08001b6e   0x08001b6e   0x00000002   PAD
    0x08001b70   0x08001b70   0x00000050   Code   RO          254    i.HAL_TIM_MspPostInit  tim.o
    0x08001bc0   0x08001bc0   0x000000cc   Code   RO          574    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08001c8c   0x08001c8c   0x0000005a   Code   RO          577    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08001ce6   0x08001ce6   0x00000002   PAD
    0x08001ce8   0x08001ce8   0x00000028   Code   RO          256    i.HAL_TIM_PWM_MspInit  tim.o
    0x08001d10   0x08001d10   0x000000c8   Code   RO          582    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08001dd8   0x08001dd8   0x00000002   Code   RO         2635    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08001dda   0x08001dda   0x00000002   PAD
    0x08001ddc   0x08001ddc   0x00000018   Code   RO         3160    i.HAL_UART_ErrorCallback  my_uart.o
    0x08001df4   0x08001df4   0x00000280   Code   RO         2652    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08002074   0x08002074   0x00000064   Code   RO         2653    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x080020d8   0x080020d8   0x000000e8   Code   RO          341    i.HAL_UART_MspInit  usart.o
    0x080021c0   0x080021c0   0x0000001c   Code   RO         2658    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x080021dc   0x080021dc   0x0000003c   Code   RO         3161    i.HAL_UART_RxCpltCallback  my_uart.o
    0x08002218   0x08002218   0x00000084   Code   RO         2662    i.HAL_UART_Transmit_DMA  stm32f4xx_hal_uart.o
    0x0800229c   0x0800229c   0x00000018   Code   RO         3162    i.HAL_UART_TxCpltCallback  my_uart.o
    0x080022b4   0x080022b4   0x00000002   Code   RO         2665    i.HAL_UART_TxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x080022b6   0x080022b6   0x00000002   Code   RO          386    i.HardFault_Handler  stm32f4xx_it.o
    0x080022b8   0x080022b8   0x00000048   Code   RO         4214    i.IIC_Ack           iic.o
    0x08002300   0x08002300   0x0000000c   Code   RO         4216    i.IIC_Delay         iic.o
    0x0800230c   0x0800230c   0x0000003c   Code   RO         4217    i.IIC_GPIO_Init     iic.o
    0x08002348   0x08002348   0x00000038   Code   RO         4218    i.IIC_NAck          iic.o
    0x08002380   0x08002380   0x00000064   Code   RO         4219    i.IIC_Read_Byte     iic.o
    0x080023e4   0x080023e4   0x00000068   Code   RO         4220    i.IIC_Send_Byte     iic.o
    0x0800244c   0x0800244c   0x00000044   Code   RO         4221    i.IIC_Start         iic.o
    0x08002490   0x08002490   0x00000034   Code   RO         4222    i.IIC_Stop          iic.o
    0x080024c4   0x080024c4   0x0000004c   Code   RO         4223    i.IIC_Wait_Ack      iic.o
    0x08002510   0x08002510   0x00000034   Code   RO         3120    i.Key_Read          key.o
    0x08002544   0x08002544   0x00000032   Code   RO         3510    i.MPU_Get_Gyro_Offset  mpu6050.o
    0x08002576   0x08002576   0x00000076   Code   RO         3513    i.MPU_Init          mpu6050.o
    0x080025ec   0x080025ec   0x0000003a   Code   RO         3514    i.MPU_Read_Byte     mpu6050.o
    0x08002626   0x08002626   0x00000002   PAD
    0x08002628   0x08002628   0x0000006c   Code   RO         3515    i.MPU_Read_Len      mpu6050.o
    0x08002694   0x08002694   0x0000000a   Code   RO         3516    i.MPU_Set_Accel_Fsr  mpu6050.o
    0x0800269e   0x0800269e   0x0000000a   Code   RO         3517    i.MPU_Set_Gyro_Fsr  mpu6050.o
    0x080026a8   0x080026a8   0x00000030   Code   RO         3518    i.MPU_Set_LPF       mpu6050.o
    0x080026d8   0x080026d8   0x0000002e   Code   RO         3519    i.MPU_Set_Rate      mpu6050.o
    0x08002706   0x08002706   0x0000003c   Code   RO         3520    i.MPU_Write_Byte    mpu6050.o
    0x08002742   0x08002742   0x00000052   Code   RO         3521    i.MPU_Write_Len     mpu6050.o
    0x08002794   0x08002794   0x0000003c   Code   RO          226    i.MX_DMA_Init       dma.o
    0x080027d0   0x080027d0   0x00000120   Code   RO          202    i.MX_GPIO_Init      gpio.o
    0x080028f0   0x080028f0   0x00000034   Code   RO          257    i.MX_TIM14_Init     tim.o
    0x08002924   0x08002924   0x00000070   Code   RO          258    i.MX_TIM1_Init      tim.o
    0x08002994   0x08002994   0x0000006c   Code   RO          259    i.MX_TIM2_Init      tim.o
    0x08002a00   0x08002a00   0x00000084   Code   RO          260    i.MX_TIM3_Init      tim.o
    0x08002a84   0x08002a84   0x00000038   Code   RO          342    i.MX_USART1_UART_Init  usart.o
    0x08002abc   0x08002abc   0x00000002   Code   RO          387    i.MemManage_Handler  stm32f4xx_it.o
    0x08002abe   0x08002abe   0x00000002   PAD
    0x08002ac0   0x08002ac0   0x00000024   Code   RO         3291    i.Motor_App_Forward  motor_app.o
    0x08002ae4   0x08002ae4   0x00000050   Code   RO         3292    i.Motor_App_Init    motor_app.o
    0x08002b34   0x08002b34   0x00000040   Code   RO         3293    i.Motor_App_Set_Speed  motor_app.o
    0x08002b74   0x08002b74   0x00000018   Code   RO         3294    i.Motor_App_Stop    motor_app.o
    0x08002b8c   0x08002b8c   0x00000002   Code   RO         3295    i.Motor_App_Task    motor_app.o
    0x08002b8e   0x08002b8e   0x0000006e   Code   RO         3363    i.Motor_Config_Init  motor_driver.o
    0x08002bfc   0x08002bfc   0x0000001e   Code   RO         3364    i.Motor_Dead_Compensation  motor_driver.o
    0x08002c1a   0x08002c1a   0x00000012   Code   RO         3365    i.Motor_Limit_Speed  motor_driver.o
    0x08002c2c   0x08002c2c   0x000000a4   Code   RO         3366    i.Motor_Set_Speed   motor_driver.o
    0x08002cd0   0x08002cd0   0x00000044   Code   RO         3367    i.Motor_Stop        motor_driver.o
    0x08002d14   0x08002d14   0x00000048   Code   RO         3424    i.Mpu6050_Init      mpu6050_app.o
    0x08002d5c   0x08002d5c   0x00000050   Code   RO         3425    i.Mpu6050_Task      mpu6050_app.o
    0x08002dac   0x08002dac   0x00000002   Code   RO          388    i.NMI_Handler       stm32f4xx_it.o
    0x08002dae   0x08002dae   0x00000002   Code   RO          389    i.PendSV_Handler    stm32f4xx_it.o
    0x08002db0   0x08002db0   0x00000014   Code   RO         3426    i.Reset_Target_Direction  mpu6050_app.o
    0x08002dc4   0x08002dc4   0x00000002   Code   RO          390    i.SVC_Handler       stm32f4xx_it.o
    0x08002dc6   0x08002dc6   0x00000004   Code   RO          391    i.SysTick_Handler   stm32f4xx_it.o
    0x08002dca   0x08002dca   0x00000002   PAD
    0x08002dcc   0x08002dcc   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x08002e60   0x08002e60   0x00000010   Code   RO         2987    i.SystemInit        system_stm32f4xx.o
    0x08002e70   0x08002e70   0x000000d0   Code   RO          595    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08002f40   0x08002f40   0x0000001a   Code   RO          596    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08002f5a   0x08002f5a   0x00000002   PAD
    0x08002f5c   0x08002f5c   0x00000060   Code   RO          608    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08002fbc   0x08002fbc   0x0000006c   Code   RO          609    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08003028   0x08003028   0x00000068   Code   RO          610    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08003090   0x08003090   0x00000050   Code   RO          611    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x080030e0   0x080030e0   0x0000000e   Code   RO         2666    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080030ee   0x080030ee   0x0000004a   Code   RO         2667    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08003138   0x08003138   0x00000042   Code   RO         2672    i.UART_DMATransmitCplt  stm32f4xx_hal_uart.o
    0x0800317a   0x0800317a   0x0000000a   Code   RO         2674    i.UART_DMATxHalfCplt  stm32f4xx_hal_uart.o
    0x08003184   0x08003184   0x0000004e   Code   RO         2676    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x080031d2   0x080031d2   0x0000001c   Code   RO         2677    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x080031ee   0x080031ee   0x000000c2   Code   RO         2678    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080032b0   0x080032b0   0x0000010c   Code   RO         2679    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080033bc   0x080033bc   0x00000036   Code   RO         2681    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x080033f2   0x080033f2   0x00000002   PAD
    0x080033f4   0x080033f4   0x0000000c   Code   RO          392    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08003400   0x08003400   0x00000002   Code   RO          393    i.UsageFault_Handler  stm32f4xx_it.o
    0x08003402   0x08003402   0x00000002   PAD
    0x08003404   0x08003404   0x00000034   Code   RO         4335    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08003438   0x08003438   0x00000030   Code   RO         4403    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08003468   0x08003468   0x00000020   Code   RO         2233    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08003488   0x08003488   0x00000360   Code   RO         4288    i.__hardfp_asin     m_wm.l(asin.o)
    0x080037e8   0x080037e8   0x000002d8   Code   RO         4369    i.__hardfp_atan     m_wm.l(atan.o)
    0x08003ac0   0x08003ac0   0x00000200   Code   RO         4302    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x08003cc0   0x08003cc0   0x000000f8   Code   RO         4405    i.__kernel_poly     m_wm.l(poly.o)
    0x08003db8   0x08003db8   0x00000014   Code   RO         4384    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x08003dcc   0x08003dcc   0x00000014   Code   RO         4385    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x08003de0   0x08003de0   0x00000020   Code   RO         4386    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08003e00   0x08003e00   0x00000020   Code   RO         4389    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08003e20   0x08003e20   0x0000000e   Code   RO         4475    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08003e2e   0x08003e2e   0x00000002   Code   RO         4476    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003e30   0x08003e30   0x0000000e   Code   RO         4477    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08003e3e   0x08003e3e   0x00000002   PAD
    0x08003e40   0x08003e40   0x0000000c   Code   RO         4449    i.__set_errno       mc_w.l(errno.o)
    0x08003e4c   0x08003e4c   0x00000184   Code   RO         4337    i._fp_digits        mc_w.l(printfa.o)
    0x08003fd0   0x08003fd0   0x000006b4   Code   RO         4338    i._printf_core      mc_w.l(printfa.o)
    0x08004684   0x08004684   0x00000024   Code   RO         4339    i._printf_post_padding  mc_w.l(printfa.o)
    0x080046a8   0x080046a8   0x0000002e   Code   RO         4340    i._printf_pre_padding  mc_w.l(printfa.o)
    0x080046d6   0x080046d6   0x00000016   Code   RO         4341    i._snputc           mc_w.l(printfa.o)
    0x080046ec   0x080046ec   0x00000090   Code   RO         3684    i.accel_self_test   inv_mpu.o
    0x0800477c   0x0800477c   0x00000010   Code   RO         4371    i.atan              m_wm.l(atan.o)
    0x0800478c   0x0800478c   0x00000060   Code   RO         3428    i.convert_to_continuous_yaw  mpu6050_app.o
    0x080047ec   0x080047ec   0x0000003a   Code   RO         4029    i.dmp_enable_6x_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08004826   0x08004826   0x00000002   PAD
    0x08004828   0x08004828   0x000001d8   Code   RO         4030    i.dmp_enable_feature  inv_mpu_dmp_motion_driver.o
    0x08004a00   0x08004a00   0x0000003c   Code   RO         4031    i.dmp_enable_gyro_cal  inv_mpu_dmp_motion_driver.o
    0x08004a3c   0x08004a3c   0x0000003a   Code   RO         4032    i.dmp_enable_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08004a76   0x08004a76   0x00000002   PAD
    0x08004a78   0x08004a78   0x00000014   Code   RO         4037    i.dmp_load_motion_driver_firmware  inv_mpu_dmp_motion_driver.o
    0x08004a8c   0x08004a8c   0x00000168   Code   RO         4038    i.dmp_read_fifo     inv_mpu_dmp_motion_driver.o
    0x08004bf4   0x08004bf4   0x000000d8   Code   RO         4041    i.dmp_set_accel_bias  inv_mpu_dmp_motion_driver.o
    0x08004ccc   0x08004ccc   0x00000064   Code   RO         4042    i.dmp_set_fifo_rate  inv_mpu_dmp_motion_driver.o
    0x08004d30   0x08004d30   0x000000cc   Code   RO         4043    i.dmp_set_gyro_bias  inv_mpu_dmp_motion_driver.o
    0x08004dfc   0x08004dfc   0x0000010c   Code   RO         4045    i.dmp_set_orientation  inv_mpu_dmp_motion_driver.o
    0x08004f08   0x08004f08   0x00000030   Code   RO         4048    i.dmp_set_shake_reject_thresh  inv_mpu_dmp_motion_driver.o
    0x08004f38   0x08004f38   0x00000020   Code   RO         4049    i.dmp_set_shake_reject_time  inv_mpu_dmp_motion_driver.o
    0x08004f58   0x08004f58   0x00000020   Code   RO         4050    i.dmp_set_shake_reject_timeout  inv_mpu_dmp_motion_driver.o
    0x08004f78   0x08004f78   0x00000040   Code   RO         4051    i.dmp_set_tap_axes  inv_mpu_dmp_motion_driver.o
    0x08004fb8   0x08004fb8   0x00000022   Code   RO         4052    i.dmp_set_tap_count  inv_mpu_dmp_motion_driver.o
    0x08004fda   0x08004fda   0x00000002   PAD
    0x08004fdc   0x08004fdc   0x00000160   Code   RO         4053    i.dmp_set_tap_thresh  inv_mpu_dmp_motion_driver.o
    0x0800513c   0x0800513c   0x00000020   Code   RO         4054    i.dmp_set_tap_time  inv_mpu_dmp_motion_driver.o
    0x0800515c   0x0800515c   0x00000020   Code   RO         4055    i.dmp_set_tap_time_multi  inv_mpu_dmp_motion_driver.o
    0x0800517c   0x0800517c   0x00000018   Code   RO         4399    i.fabs              m_wm.l(fabs.o)
    0x08005194   0x08005194   0x000000b4   Code   RO         3685    i.get_accel_prod_shift  inv_mpu.o
    0x08005248   0x08005248   0x00000314   Code   RO         3686    i.get_st_biases     inv_mpu.o
    0x0800555c   0x0800555c   0x000001b0   Code   RO         3024    i.gray_task         scheduler.o
    0x0800570c   0x0800570c   0x000000d4   Code   RO         3687    i.gyro_self_test    inv_mpu.o
    0x080057e0   0x080057e0   0x00000022   Code   RO         3688    i.inv_orientation_matrix_to_scalar  inv_mpu.o
    0x08005802   0x08005802   0x00000048   Code   RO         3689    i.inv_row_2_scale   inv_mpu.o
    0x0800584a   0x0800584a   0x00000002   PAD
    0x0800584c   0x0800584c   0x0000004c   Code   RO         3121    i.key_task          key.o
    0x08005898   0x08005898   0x0000008c   Code   RO           15    i.main              main.o
    0x08005924   0x08005924   0x00000002   Code   RO         3690    i.mget_ms           inv_mpu.o
    0x08005926   0x08005926   0x00000010   Code   RO         3025    i.motor_task        scheduler.o
    0x08005936   0x08005936   0x00000002   PAD
    0x08005938   0x08005938   0x00000054   Code   RO         3691    i.mpu_configure_fifo  inv_mpu.o
    0x0800598c   0x0800598c   0x00000164   Code   RO         3692    i.mpu_dmp_get_data  inv_mpu.o
    0x08005af0   0x08005af0   0x000000ac   Code   RO         3693    i.mpu_dmp_init      inv_mpu.o
    0x08005b9c   0x08005b9c   0x0000003c   Code   RO         3694    i.mpu_get_accel_fsr  inv_mpu.o
    0x08005bd8   0x08005bd8   0x00000044   Code   RO         3696    i.mpu_get_accel_sens  inv_mpu.o
    0x08005c1c   0x08005c1c   0x00000034   Code   RO         3702    i.mpu_get_gyro_fsr  inv_mpu.o
    0x08005c50   0x08005c50   0x0000004c   Code   RO         3704    i.mpu_get_gyro_sens  inv_mpu.o
    0x08005c9c   0x08005c9c   0x00000038   Code   RO         3706    i.mpu_get_lpf       inv_mpu.o
    0x08005cd4   0x08005cd4   0x0000001c   Code   RO         3708    i.mpu_get_sample_rate  inv_mpu.o
    0x08005cf0   0x08005cf0   0x00000124   Code   RO         3710    i.mpu_init          inv_mpu.o
    0x08005e14   0x08005e14   0x000000a0   Code   RO         3711    i.mpu_load_firmware  inv_mpu.o
    0x08005eb4   0x08005eb4   0x000000b8   Code   RO         3712    i.mpu_lp_accel_mode  inv_mpu.o
    0x08005f6c   0x08005f6c   0x00000090   Code   RO         3715    i.mpu_read_fifo_stream  inv_mpu.o
    0x08005ffc   0x08005ffc   0x00000054   Code   RO         3716    i.mpu_read_mem      inv_mpu.o
    0x08006050   0x08006050   0x00000160   Code   RO         3719    i.mpu_reset_fifo    inv_mpu.o
    0x080061b0   0x080061b0   0x000000f0   Code   RO         3720    i.mpu_run_self_test  inv_mpu.o
    0x080062a0   0x080062a0   0x00000068   Code   RO         3722    i.mpu_set_accel_fsr  inv_mpu.o
    0x08006308   0x08006308   0x000000dc   Code   RO         3723    i.mpu_set_bypass    inv_mpu.o
    0x080063e4   0x080063e4   0x00000078   Code   RO         3725    i.mpu_set_dmp_state  inv_mpu.o
    0x0800645c   0x0800645c   0x0000006c   Code   RO         3726    i.mpu_set_gyro_fsr  inv_mpu.o
    0x080064c8   0x080064c8   0x00000064   Code   RO         3727    i.mpu_set_int_latched  inv_mpu.o
    0x0800652c   0x0800652c   0x0000006c   Code   RO         3729    i.mpu_set_lpf       inv_mpu.o
    0x08006598   0x08006598   0x00000080   Code   RO         3730    i.mpu_set_sample_rate  inv_mpu.o
    0x08006618   0x08006618   0x000000b8   Code   RO         3731    i.mpu_set_sensors   inv_mpu.o
    0x080066d0   0x080066d0   0x00000004   Code   RO         3026    i.mpu_task          scheduler.o
    0x080066d4   0x080066d4   0x00000054   Code   RO         3732    i.mpu_write_mem     inv_mpu.o
    0x08006728   0x08006728   0x00000058   Code   RO         3164    i.my_printf_nb      my_uart.o
    0x08006780   0x08006780   0x00000088   Code   RO         3733    i.run_self_test     inv_mpu.o
    0x08006808   0x08006808   0x00000018   Code   RO         3027    i.scheduler_init    scheduler.o
    0x08006820   0x08006820   0x00000040   Code   RO         3028    i.scheduler_run     scheduler.o
    0x08006860   0x08006860   0x00000058   Code   RO         3734    i.set_int_enable    inv_mpu.o
    0x080068b8   0x080068b8   0x0000006e   Code   RO         4410    i.sqrt              m_wm.l(sqrt.o)
    0x08006926   0x08006926   0x00000002   PAD
    0x08006928   0x08006928   0x00000134   Code   RO         3029    i.uart_task         scheduler.o
    0x08006a5c   0x08006a5c   0x00000008   Data   RO         1947    .constdata          stm32f4xx_hal_dma.o
    0x08006a64   0x08006a64   0x00000010   Data   RO         2988    .constdata          system_stm32f4xx.o
    0x08006a74   0x08006a74   0x00000008   Data   RO         2989    .constdata          system_stm32f4xx.o
    0x08006a7c   0x08006a7c   0x0000001b   Data   RO         3736    .constdata          inv_mpu.o
    0x08006a97   0x08006a97   0x00000001   PAD
    0x08006a98   0x08006a98   0x0000000c   Data   RO         3737    .constdata          inv_mpu.o
    0x08006aa4   0x08006aa4   0x00000028   Data   RO         3738    .constdata          inv_mpu.o
    0x08006acc   0x08006acc   0x00000bf6   Data   RO         4057    .constdata          inv_mpu_dmp_motion_driver.o
    0x080076c2   0x080076c2   0x00000006   PAD
    0x080076c8   0x080076c8   0x00000050   Data   RO         4291    .constdata          m_wm.l(asin.o)
    0x08007718   0x08007718   0x00000098   Data   RO         4372    .constdata          m_wm.l(atan.o)
    0x080077b0   0x080077b0   0x00000008   Data   RO         4407    .constdata          m_wm.l(qnan.o)
    0x080077b8   0x080077b8   0x00000068   Data   RO         3030    .conststring        scheduler.o
    0x08007820   0x08007820   0x00000020   Data   RO         4473    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007840, Size: 0x000009f8, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007840   0x0000000c   Data   RW         2392    .data               stm32f4xx_hal.o
    0x2000000c   0x0800784c   0x00000004   Data   RW         2990    .data               system_stm32f4xx.o
    0x20000010   0x08007850   0x00000050   Data   RW         3031    .data               scheduler.o
    0x20000060   0x080078a0   0x00000007   Data   RW         3122    .data               key.o
    0x20000067   0x080078a7   0x00000001   PAD
    0x20000068   0x080078a8   0x00000008   Data   RW         3168    .data               my_uart.o
    0x20000070   0x080078b0   0x00000028   Data   RW         3240    .data               gray.o
    0x20000098   0x080078d8   0x00000024   Data   RW         3429    .data               mpu6050_app.o
    0x200000bc   0x080078fc   0x00000035   Data   RW         3739    .data               inv_mpu.o
    0x200000f1   0x08007931   0x00000003   PAD
    0x200000f4   0x08007934   0x00000004   Data   RW         4450    .data               mc_w.l(errno.o)
    0x200000f8        -       0x00000120   Zero   RW          261    .bss                tim.o
    0x20000218        -       0x00000108   Zero   RW          343    .bss                usart.o
    0x20000320        -       0x00000280   Zero   RW         3167    .bss                my_uart.o
    0x200005a0        -       0x00000048   Zero   RW         3298    .bss                motor_app.o
    0x200005e8        -       0x00000010   Zero   RW         4056    .bss                inv_mpu_dmp_motion_driver.o
    0x200005f8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08007938, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        60          4          0          0          0        754   dma.o
       288         20          0          0          0       1035   gpio.o
       288         38          0         40          0       3367   gray.o
       600         44          0          0          0       5667   iic.o
      5220        226         79         53          0      30826   inv_mpu.o
      2442        134       3062          0         16      19878   inv_mpu_dmp_motion_driver.o
       128         10          0          7          0       1391   key.o
       292         38          0          0          0     692724   main.o
       206         32          0          0         72       3302   motor_app.o
       390          0          0          0          0       3621   motor_driver.o
       590          0          0          0          0       6783   mpu6050.o
       468         76          0         36          0       5209   mpu6050_app.o
       196         46          0          8        640       3505   my_uart.o
       848         86        104         80          0       5514   scheduler.o
        36          8        392          0       1024        820   startup_stm32f407xx.o
       180         28          0         12          0       9293   stm32f4xx_hal.o
       198         14          0          0          0      33735   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7222   stm32f4xx_hal_dma.o
       516         46          0          0          0       2799   stm32f4xx_hal_gpio.o
        48          6          0          0          0        830   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5176   stm32f4xx_hal_rcc.o
      1512        108          0          0          0      10973   stm32f4xx_hal_tim.o
       144         28          0          0          0       1348   stm32f4xx_hal_tim_ex.o
      1690         26          0          0          0      11787   stm32f4xx_hal_uart.o
        56         18          0          0          0       5430   stm32f4xx_it.o
        16          4         24          4          0       1087   system_stm32f4xx.o
       732         84          0          0        288       5425   tim.o
       288         30          0          0        264       1848   usart.o

    ----------------------------------------------------------------------
     19898       <USER>       <GROUP>        244       2304     881349   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38          0          7          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       864         94         80          0          0        280   asin.o
       744        106        152          0          0        352   atan.o
       512         64          0          0          0        208   atan2.o
       104         16          0          0          0        496   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
       110          0          0          0          0        148   sqrt.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2260         86          0          0          0        528   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
       162          0          0          0          0        100   dsqrt.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      6938        <USER>        <GROUP>          4          0       4412   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2654        280        240          0          0       1884   m_wm.l
      2798        108          0          4          0       1380   mc_w.l
      1480          0          0          0          0       1148   mf_w.l

    ----------------------------------------------------------------------
      6938        <USER>        <GROUP>          4          0       4412   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     26836       1630       3948        248       2304     865765   Grand Totals
     26836       1630       3948        248       2304     865765   ELF Image Totals
     26836       1630       3948        248          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                30784 (  30.06kB)
    Total RW  Size (RW Data + ZI Data)              2552 (   2.49kB)
    Total ROM Size (Code + RO Data + RW Data)      31032 (  30.30kB)

==============================================================================

