# 🚗 模式1：直线行驶遇黑线永久停止

## 📊 功能概述

模式1实现基础的直线行驶功能，遇到黑线后永久停止：
- **直线行驶**: 小车以固定速度直线前进
- **黑线检测**: 实时监测7路传感器状态
- **永久停止**: 检测到黑线后永久停止，不再运行
- **状态锁定**: 停止后保持停止状态，直到切换模式

## 🎯 工作原理

### **控制逻辑**
```c
case 1:  // 模式1：直线行驶，遇黑线后永久停止
{
    static uint8_t permanently_stopped = 0;  // 永久停止标志

    if(permanently_stopped) {
        // 已经永久停止，不再运行
        stop_motors();
        my_printf(&huart1," PERMANENTLY_STOPPED");
    } else if(sensor_sum > 0) {
        // 检测到黑线，永久停止
        stop_motors();
        permanently_stopped = 1;  // 设置永久停止标志
        my_printf(&huart1," STOP_LINE_DETECTED_PERMANENT");
    } else {
        // 没有检测到黑线，继续直行
        move_forward(40);
        my_printf(&huart1," STRAIGHT");
    }
}
break;
```

### **永久停止机制**
```c
// 永久停止状态管理
static uint8_t permanently_stopped = 0;  // 静态变量，保持状态

// 状态判断逻辑
if(permanently_stopped) {
    // 一旦停止，永远停止
    stop_motors();
    // 不再检测传感器，不再运行
} else if(sensor_sum > 0) {
    // 首次检测到黑线
    stop_motors();
    permanently_stopped = 1;  // 设置永久停止标志
} else {
    // 正常直行状态
    move_forward(40);
}
```

### **状态转换图**
```
启动模式1 → 直线行驶 → 检测到黑线 → 永久停止
    ↑           ↑           ↑           ↑
  STRAIGHT   STRAIGHT   STOP_PERMANENT  PERMANENTLY_STOPPED
                                       ↓
                                   保持停止状态
                                   (直到切换模式)
```

## ⚙️ 技术参数

### **运行参数**
```
模式编号: 1
直行速度: 40% (适中速度)
检测范围: P_2到P_8 (7路传感器)
停止条件: 任意传感器检测到黑线
声光提示: 检测到黑线时触发
```

### **传感器配置**
```
有效传感器: P_2, P_3, P_4, P_5, P_6, P_7, P_8
忽略传感器: P_1 (硬件问题)
检测逻辑: 0=黑线, 1=白线
触发条件: 任意传感器为0
```

## 📊 串口输出

### **正常运行状态**
```
[TRACK] X1111111 Mode:1 STRAIGHT
[TRACK] X1111111 Mode:1 STRAIGHT
[TRACK] X1111111 Mode:1 STRAIGHT
```

### **检测到黑线状态**
```
[TRACK] X1111111 Mode:1 STRAIGHT
[TRACK] X0111111 Mode:1 [BEEP] STOP_LINE_DETECTED_PERMANENT
[TRACK] X0111111 Mode:1 PERMANENTLY_STOPPED
[TRACK] X0111111 Mode:1 PERMANENTLY_STOPPED
[TRACK] X1111111 Mode:1 PERMANENTLY_STOPPED
```

### **输出格式说明**
- **X1111111**: 7路传感器状态，X表示忽略的P_1
- **Mode:1**: 当前运行模式为1
- **STRAIGHT**: 直线行驶状态
- **STOP_LINE_DETECTED_PERMANENT**: 检测到黑线并永久停止
- **PERMANENTLY_STOPPED**: 已永久停止状态
- **[BEEP]**: 声光提示触发

## 🎮 操作方法

### **模式切换**
```c
// 按键控制
KEY0: sys_mode = (sys_mode+1)%4;  // 模式+1，循环0-3
KEY1: sys_mode = (sys_mode+2)%4;  // 模式+2，快速切换
KEY2: sys_ture = 1;               // 启动确认
```

### **启动步骤**
1. **切换模式**: 按KEY0切换到模式1
2. **确认模式**: 观察串口输出"Mode:1"
3. **放置小车**: 将小车放在起始位置
4. **开始运行**: 小车自动开始直线行驶
5. **观察停止**: 遇到黑线时自动停止

## 🔧 参数调整

### **速度调整**
```c
// 当前速度：40%
move_forward(40);

// 更快速度
move_forward(50);  // 提高到50%

// 更慢速度  
move_forward(30);  // 降低到30%
```

### **检测灵敏度调整**
```c
// 当前：任意传感器检测到即停止
if(sensor_sum > 0) stop_motors();

// 更严格：至少2个传感器检测到
if(sensor_sum >= 2000) stop_motors();

// 更宽松：至少3个传感器检测到
if(sensor_sum >= 3000) stop_motors();
```

## 🎯 应用场景

### **竞赛应用**
- **A→B任务**: 从起点A直行到终点B
- **起始阶段**: 竞赛开始的第一个动作
- **定点停车**: 在指定黑线位置精确停车
- **距离测量**: 测量两点间的直线距离

### **调试应用**
- **传感器测试**: 验证传感器检测功能
- **电机测试**: 验证电机直行性能
- **系统验证**: 验证基本控制功能
- **精度测试**: 测试停止位置精度

## 📈 性能特点

### **优势**
- ✅ **简单可靠**: 逻辑简单，不易出错
- ✅ **响应快速**: 检测到黑线立即停止
- ✅ **精确停止**: 在黑线位置精确停车
- ✅ **适合竞赛**: 符合竞赛A→B任务要求

### **局限性**
- ❌ **功能单一**: 只能直行，无转向能力
- ❌ **无循迹**: 不能跟随黑线行驶
- ❌ **停止后无动作**: 停止后需要切换模式

## 🔍 故障排除

### **小车不动**
```c
// 检查项目：
1. 确认模式为1 (观察串口"Mode:1")
2. 检查电机连接和供电
3. 确认move_forward函数正常
```

### **不停止**
```c
// 检查项目：
1. 传感器是否检测到黑线
2. 传感器高度是否合适(2-5mm)
3. 黑线对比度是否足够
4. 观察串口传感器状态变化
```

### **误停止**
```c
// 检查项目：
1. 传感器是否误触发
2. 环境光照是否稳定
3. 地面是否有黑色区域
```

## 📊 测试验证

### **功能测试**
```
测试1: 基础直行
- 在白色地面上测试
- 观察小车直线行驶
- 确认速度和方向正确

测试2: 黑线检测
- 在前方放置黑线
- 观察小车接近黑线
- 确认在黑线前停止

测试3: 停止精度
- 多次测试停止位置
- 测量与黑线的距离
- 精度应在±2cm内
```

### **竞赛验证**
```
场景: A→B任务模拟
- 设置起点A和终点B(黑线)
- 测量A到B的距离
- 验证小车能准确到达B点停止
```

## 🔧 代码优化建议

### **当前实现**
```c
// 简单的检测和停止
if(sensor_sum > 0) {
    stop_motors();
    my_printf(&huart1," STOP_LINE_DETECTED");
} else {
    move_forward(40);
    my_printf(&huart1," STRAIGHT");
}
```

### **优化建议**
```c
// 添加停止状态记录
static uint8_t stopped = 0;

if(sensor_sum > 0 && !stopped) {
    stop_motors();
    stopped = 1;  // 记录已停止
    my_printf(&huart1," STOP_LINE_DETECTED");
    
    // 额外的声光提示
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_SET);
    HAL_Delay(500);
    HAL_GPIO_WritePin(FMD_LED_GPIO_Port, FMD_LED_Pin, GPIO_PIN_RESET);
    
} else if(sensor_sum == 0 && !stopped) {
    move_forward(40);
    my_printf(&huart1," STRAIGHT");
} else if(stopped) {
    my_printf(&huart1," STOPPED");
}
```

## 📈 与其他模式的关系

### **模式对比**
```
模式0: 停止模式 - 完全静止
模式1: 直线遇线停止 - 基础运动 ✅
模式2: (预留) - 待定义
模式3: (预留) - 待定义
默认: 循迹模式 - 复杂控制
```

### **模式切换流程**
```
启动 → 模式0(停止) → 按键切换 → 模式1(直行) → 完成任务 → 模式0(停止)
```

## 📁 相关文档

### **参考文档**
- `Mode0_Stop_Mode.md` - 模式0停止模式
- `Simple_Line_Following_Control.md` - 默认循迹控制
- `7_Sensor_Configuration.md` - 7路传感器配置

### **代码文件**
- `APP/scheduler.c` - 主控制逻辑
- `APP/Gray.c` - 传感器读取
- `APP/car.c` - 运动控制
- `APP/key.c` - 按键控制

---

**实现完成时间**: 2025-06-20  
**模式类型**: 基础直行模式  
**特点**: 简单可靠 + 精确停止 + 竞赛适用  
**状态**: ✅ 已实现
