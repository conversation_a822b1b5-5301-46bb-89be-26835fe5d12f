# 🛤️ 基于灰度传感器的循迹功能实现

## 📊 功能概述

实现了完整的基于灰度传感器的自动循迹功能：
- **7传感器循迹**: 使用P_2到P_8进行线位置检测
- **智能转向**: 根据线位置自动调整左右轮速度
- **搜索模式**: 丢失线时自动搜索
- **实时监控**: 串口输出循迹状态和调试信息

## 🎯 循迹算法原理

### **线位置检测**

```c
// 加权平均算法
for(int i = 1; i < 8; i++) {  // 跳过P_1
    if(gray_buff[i] == 0) {  // 检测到黑线
        line_position += i * 1000;  // 位置权重
        sensor_sum += 1000;
    }
}
line_position = line_position / sensor_sum;  // 计算平均位置
```

### **位置权重分配**

```
传感器:  [X] [1] [2] [3] [4] [5] [6] [7]
权重:    忽略 1000 2000 3000 4000 5000 6000 7000
位置:    P_1  P_2  P_3  P_4  P_5  P_6  P_7  P_8
```

## 🚗 控制策略

### **精细化转向控制**

```c
if(line_position < 3000) {
    // 线在左侧，左转
    move_custom(25, 45);
} else if(line_position < 3500) {
    // 线在左偏，轻微左转
    move_custom(30, 40);
} else if(line_position > 6000) {
    // 线在右侧，右转
    move_custom(45, 25);
} else if(line_position > 5500) {
    // 线在右偏，轻微右转
    move_custom(40, 30);
} else {
    // 线在中间，直行
    move_forward(35);
}
```

### **转向策略说明**

- **急转弯**: 速度差大 (25 vs 45)，转向快
- **微调**: 速度差小 (30 vs 40)，转向缓
- **直行**: 双轮同速 (35)，保持直线

## 🔍 丢线处理

### **直线前进策略**
```c
if(sensor_sum == 0) {  // 没有检测到线
    // 保持直线前进
    move_forward(35);
}
```

### **处理特点**
- **保持运动**: 丢线时继续直线前进
- **简单可靠**: 避免复杂的搜索逻辑
- **连续性好**: 保持运动的连续性
- **便于调试**: 明确的前进状态

## 📊 串口监控

### **输出格式**
```
[TRACK] X1110111 Pos:3500
[TRACK] X1100111 Pos:2500
[TRACK] X1000111 Pos:2000
[TRACK] X1111111 NO_LINE
```

### **信息解读**
- **[TRACK]**: 循迹模式标识
- **X1110111**: 8个传感器状态 (X=P_1忽略)
- **Pos:3500**: 线位置值 (1000-7000)
- **NO_LINE**: 未检测到线，已停止

## 🎮 循迹行为

### **典型循迹过程**
```
1. 直线跟踪:
[TRACK] X1110111 Pos:4000 → 直行

2. 遇到左弯:
[TRACK] X0111111 Pos:2000 → 左转

3. 弯道中:
[TRACK] X0011111 Pos:1500 → 急左转

4. 回到直线:
[TRACK] X1110111 Pos:4000 → 直行

5. 遇到右弯:
[TRACK] X1111100 Pos:6500 → 右转

6. 丢线前进:
[TRACK] X1111111 NO_LINE → 直线前进
```

### **速度控制**

- **直行速度**: 35% (稳定跟线)
- **微调速度**: 30-40% (精细调整)
- **转弯速度**: 25-45% (快速转向)
- **丢线处理**: 35%直线前进

## ⚙️ 系统配置

### **任务调度**
```c
static task_t scheduler_task[] =
{
    {car_task,20,0},
    {key_task,10,0},
    {uart_task,150,0},
    //{pid_speed_task,30,0},  // PID控制暂停
    {gray_task,30,0},       // 循迹任务30ms
};
```

### **控制频率**

- **循迹频率**: 30ms (33.3Hz)
- **传感器读取**: 实时
- **串口输出**: 每次循迹更新
- **丢线处理**: 直线前进

## 🔧 参数调整

### **转向灵敏度调整**

```c
// 更敏感的转向
if(line_position < 3200) turn_left();   // 降低阈值
if(line_position > 5800) turn_right();  // 提高阈值

// 更保守的转向
if(line_position < 2800) turn_left();   // 提高阈值
if(line_position > 6200) turn_right();  // 降低阈值
```

### **速度调整**
```c
// 更快的循迹
move_forward(50);        // 提高直行速度
move_custom(35, 55);     // 提高转弯速度

// 更稳的循迹
move_forward(25);        // 降低直行速度
move_custom(20, 35);     // 降低转弯速度
```

### **搜索参数调整**
```c
// 更快的搜索
if(HAL_GetTick() - search_time > 100) // 100ms切换

// 更慢的搜索
if(HAL_GetTick() - search_time > 300) // 300ms切换
```

## 📈 性能特点

### **优点**

- ✅ **自动循迹**: 无需人工干预
- ✅ **智能搜索**: 丢线自动恢复
- ✅ **精细控制**: 5级转向控制
- ✅ **实时监控**: 完整的状态输出
- ✅ **容错性强**: P_1故障不影响功能

### **适用场景**

- **黑线循迹**: 白底黑线的标准赛道
- **室内环境**: 光照稳定的室内场地
- **中低速**: 适合稳定精确的循迹
- **教学演示**: 清晰的工作过程展示

## 🔍 故障排除

### **循迹不稳定**

```c
// 可能原因：传感器高度不当
// 解决：调整传感器到地面距离

// 可能原因：光照影响
// 解决：在稳定光照环境下测试

// 可能原因：线宽不合适
// 解决：使用2-3cm宽的黑线
```

### **转向过度**

```c
// 降低转向速度差
move_custom(30, 35);  // 减小速度差

// 调整转向阈值
if(line_position < 2500) turn_left();  // 提高阈值
```

### **搜索效果差**

```c
// 调整搜索速度
turn_left(40);   // 提高搜索速度

// 调整切换频率
if(HAL_GetTick() - search_time > 150) // 调整频率
```

## 🎯 使用建议

### **赛道要求**
- **线宽**: 2-3cm黑线
- **底色**: 白色或浅色背景
- **弯道**: 半径不小于20cm
- **光照**: 均匀稳定的室内光照

### **调试步骤**
1. **静态测试**: 手动移动观察传感器输出
2. **直线测试**: 在直线上测试跟线效果
3. **弯道测试**: 测试左右转弯性能
4. **搜索测试**: 故意丢线测试搜索功能

### **优化建议**
1. **先调直线**: 确保直线跟踪稳定
2. **再调转弯**: 逐步优化转弯参数
3. **最后调搜索**: 完善丢线恢复功能

---

**实现完成时间**: 2025-06-20  
**功能**: 完整的灰度传感器循迹系统  
**特色**: 智能搜索 + 精细控制 + 实时监控  
**状态**: ✅ 已实现并启用
