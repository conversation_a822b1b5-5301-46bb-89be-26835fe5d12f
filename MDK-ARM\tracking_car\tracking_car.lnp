--cpu=Cortex-M4.fp.sp
"tracking_car\startup_stm32f407xx.o"
"tracking_car\main.o"
"tracking_car\gpio.o"
"tracking_car\dma.o"
"tracking_car\tim.o"
"tracking_car\usart.o"
"tracking_car\stm32f4xx_it.o"
"tracking_car\stm32f4xx_hal_msp.o"
"tracking_car\stm32f4xx_hal_tim.o"
"tracking_car\stm32f4xx_hal_tim_ex.o"
"tracking_car\stm32f4xx_hal_rcc.o"
"tracking_car\stm32f4xx_hal_rcc_ex.o"
"tracking_car\stm32f4xx_hal_flash.o"
"tracking_car\stm32f4xx_hal_flash_ex.o"
"tracking_car\stm32f4xx_hal_flash_ramfunc.o"
"tracking_car\stm32f4xx_hal_gpio.o"
"tracking_car\stm32f4xx_hal_dma_ex.o"
"tracking_car\stm32f4xx_hal_dma.o"
"tracking_car\stm32f4xx_hal_pwr.o"
"tracking_car\stm32f4xx_hal_pwr_ex.o"
"tracking_car\stm32f4xx_hal_cortex.o"
"tracking_car\stm32f4xx_hal.o"
"tracking_car\stm32f4xx_hal_exti.o"
"tracking_car\stm32f4xx_hal_uart.o"
"tracking_car\system_stm32f4xx.o"
"tracking_car\scheduler.o"
"tracking_car\key.o"
"tracking_car\my_uart.o"
"tracking_car\gray.o"
"tracking_car\mpu6050_app.o"
"tracking_car\mpu6050.o"
"tracking_car\pid.o"
"tracking_car\inv_mpu.o"
"tracking_car\inv_mpu_dmp_motion_driver.o"
"tracking_car\iic.o"
"tracking_car\car.o"
--library_type=microlib --strict --scatter "tracking_car\tracking_car.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "tracking_car.map" -o tracking_car\tracking_car.axf