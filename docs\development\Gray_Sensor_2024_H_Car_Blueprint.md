# 🎯 基于2024_H_Car蓝本的灰度传感器优化

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: 已完成
- **目的**: 基于2024_H_Car（New）蓝本优化灰度传感器巡线算法

## 🎯 优化目标

### 蓝本分析
从2024_H_Car（New）项目中提取的核心巡线算法：
- **权重配置**: 8路传感器使用 -4.0f 到 +4.0f 的权重
- **加权平均**: 基于激活传感器的加权平均计算位置误差
- **PID控制**: 使用位置误差进行速度修正

### 优化目标
- **精确定位**: 使用加权平均算法精确计算线位置
- **平滑控制**: 基于位置误差的连续控制
- **稳定性**: 减少震荡，提升跟踪稳定性

## 🔧 核心算法实现

### 1. 权重配置 (基于蓝本)
```c
// 8路灰度传感器权重配置 (基于2024_H_Car蓝本)
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};

// 权重说明:
// P_1: -4.0f (最左侧，硬件故障忽略)
// P_2: -3.0f (左侧)
// P_3: -2.0f (左中)
// P_4: -1.0f (左中心)
// P_5:  1.0f (右中心)
// P_6:  2.0f (右中)
// P_7:  3.0f (右侧)
// P_8:  4.0f (最右侧)
```

### 2. 位置误差计算 (基于蓝本算法)
```c
void Gray_Task(void)
{
    uint8_t gray_buf[8];
    Gray_get(gray_buf);
    
    // 将数组数据转换为数字位数据 (基于蓝本格式)
    gray_digital_data = 0;
    for(uint8_t i = 0; i < 8; i++) {
        if(gray_buf[i] == 0) {  // 0表示检测到黑线
            gray_digital_data |= (1 << i);
        }
    }
    
    // 基于2024_H_Car蓝本的巡线算法
    float weighted_sum = 0;
    uint8_t black_line_count = 0;
    
    for(uint8_t i = 0; i < 8; i++) {
        if((gray_digital_data >> i) & 0x01) {
            weighted_sum += gray_weights[i];
            black_line_count++;
        }
    }
    
    // 计算位置误差 (基于蓝本算法)
    if(black_line_count > 0) {
        g_line_position_error = weighted_sum / (float)black_line_count;
    }
    // 没有检测到黑线时，保持上次的误差值
}
```

### 3. PID控制逻辑 (基于蓝本)
```c
// 在scheduler.c的模式2中应用
if(active_sensors > 0) {
    // 基于2024_H_Car蓝本的PID控制逻辑
    // position_error范围: -4.0 到 +4.0
    // 负值表示偏左，正值表示偏右
    
    int8_t speed_correction = (int8_t)(position_error * 3.0f);  // 调整系数
    
    // 限制修正量
    if(speed_correction > 10) speed_correction = 10;
    if(speed_correction < -10) speed_correction = -10;
    
    int8_t left_speed = base_speed - speed_correction;
    int8_t right_speed = base_speed + speed_correction;
    
    // 速度限制
    if(left_speed < 5) left_speed = 5;
    if(left_speed > 40) left_speed = 40;
    if(right_speed < 5) right_speed = 5;
    if(right_speed > 40) right_speed = 40;
    
    move_custom(left_speed, right_speed);
}
```

## 📊 算法对比分析

### 原算法 vs 蓝本算法

| 特性 | 原算法 | 蓝本算法 | 改善 |
|------|--------|----------|------|
| 位置计算 | 离散判断 | 加权平均 | 连续精确 |
| 控制方式 | 分段控制 | PID控制 | 平滑连续 |
| 权重分配 | 固定权重 | 浮点权重 | 精度提升 |
| 误差范围 | 离散值 | -4.0~+4.0 | 连续范围 |
| 稳定性 | 易震荡 | 平滑控制 | 显著提升 |

### 核心优势

#### 1. 精确定位
- **连续计算**: 使用加权平均而非离散判断
- **浮点精度**: 位置误差精确到小数点后两位
- **全范围覆盖**: -4.0到+4.0的连续误差范围

#### 2. 平滑控制
- **比例控制**: 速度修正与位置误差成比例
- **限幅保护**: 防止过度修正
- **连续调节**: 避免突变和震荡

#### 3. 稳定性提升
- **减少震荡**: 平滑的控制曲线
- **快速响应**: 保持对线路变化的快速响应
- **鲁棒性**: 对传感器噪声的抗干扰能力

## 🎮 实际应用效果

### 模式2循迹优化

#### 控制逻辑
```c
case 2:  // 模式2：基于2024_H_Car蓝本的循迹模式
{	
    // 执行基于蓝本的灰度传感器任务
    Gray_Task();
    
    // 获取巡线位置误差 (基于蓝本算法)
    float position_error = Gray_Get_Position_Error();
    uint8_t active_sensors = Gray_Get_Active_Sensor_Count();
    
    // 基本参数
    uint8_t base_speed = 20;
    
    if(active_sensors > 0) {
        // PID控制逻辑
        int8_t speed_correction = (int8_t)(position_error * 3.0f);
        
        // 限制修正量和速度
        // ...
        
        move_custom(left_speed, right_speed);
    } else {
        // 失线处理
        move_forward(base_speed);
    }
}
```

#### 输出格式优化
```
P2:0 P3:1 P4:1 P5:0 P6:0 P7:0 P8:0 | ERR:-1.50 ACT:2 | L:23F R:17F | MODE2
```

新增信息：
- **ERR**: 位置误差值 (-4.0到+4.0)
- **ACT**: 激活传感器数量
- **L/R**: 左右轮实际速度

## 🔧 参数调优

### 关键参数

#### 1. 权重配置
```c
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};
```
- **可调范围**: ±1.0f 到 ±8.0f
- **调整原则**: 保持对称，中心权重小，边缘权重大

#### 2. 控制系数
```c
int8_t speed_correction = (int8_t)(position_error * 3.0f);  // 调整系数
```
- **当前值**: 3.0f
- **可调范围**: 1.0f 到 6.0f
- **调整效果**: 值越大，转向越敏感

#### 3. 速度限制
```c
// 速度限制
if(left_speed < 5) left_speed = 5;      // 最小速度
if(left_speed > 40) left_speed = 40;    // 最大速度
```
- **最小速度**: 5 (防止停转)
- **最大速度**: 40 (防止失控)
- **基础速度**: 20 (平衡点)

### 调优建议

#### 1. 初始调试
1. 使用默认参数进行基础测试
2. 观察位置误差的变化范围
3. 检查速度修正的效果

#### 2. 精细调优
1. **权重调整**: 根据传感器布局微调权重
2. **系数调整**: 根据响应速度调整控制系数
3. **速度调整**: 根据赛道特点调整速度范围

#### 3. 稳定性验证
1. 长时间运行测试
2. 不同赛道条件测试
3. 干扰环境测试

## 📈 性能监控

### 调试输出
```c
my_printf(&huart1," TRACK_ERR:%.2f L:%d R:%d", position_error, left_speed, right_speed);
```

### 关键指标
- **位置误差**: 应在±2.0范围内波动
- **速度差异**: 左右轮速度差应合理
- **响应时间**: 误差变化到速度调整的延迟
- **稳定性**: 直线段的误差波动幅度

## 🎯 未来扩展

### 1. 自适应权重
- 根据传感器响应特性自动调整权重
- 动态优化权重分配

### 2. 高级PID
- 添加积分和微分项
- 实现完整的PID控制

### 3. 机器学习
- 学习最优控制参数
- 自适应环境变化

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**技术支持**: STM32循迹小车开发团队
