#include "encoder.h"

// 左右编码器实例
Encoder_t left_encoder;
Encoder_t right_encoder;

// 编码器初始化
void encoder_init(void)
{
    // 左编码器配置 (TIM1)
    left_encoder.htim = &htim1;
    left_encoder.reverse = 0;  // 根据实际安装方向调整
    left_encoder.count = 0;
    left_encoder.total_count = 0;
    left_encoder.speed_cm_s = 0.0f;
    left_encoder.speed_rpm = 0.0f;
    
    // 右编码器配置 (TIM2)
    right_encoder.htim = &htim2;
    right_encoder.reverse = 1;  // 右电机通常需要反向
    right_encoder.count = 0;
    right_encoder.total_count = 0;
    right_encoder.speed_cm_s = 0.0f;
    right_encoder.speed_rpm = 0.0f;
    
    // 启动编码器定时器
    HAL_TIM_Encoder_Start(&htim1, TIM_CHANNEL_ALL);
    HAL_TIM_Encoder_Start(&htim2, TIM_CHANNEL_ALL);
    
    // 清零计数器
    __HAL_TIM_SetCounter(&htim1, 0);
    __HAL_TIM_SetCounter(&htim2, 0);
}

// 更新单个编码器数据
static void encoder_update_single(Encoder_t *encoder)
{
    // 读取计数值
    encoder->count = (int16_t)__HAL_TIM_GetCounter(encoder->htim);
    
    // 处理反向
    if(encoder->reverse) {
        encoder->count = -encoder->count;
    }
    
    // 清零硬件计数器
    __HAL_TIM_SetCounter(encoder->htim, 0);
    
    // 累计总数
    encoder->total_count += encoder->count;
    
    // 计算线速度 (cm/s)
    encoder->speed_cm_s = (float)encoder->count / ENCODER_PPR * WHEEL_CIRCUMFERENCE_CM / SAMPLING_TIME_S;
    
    // 计算转速 (RPM)
    encoder->speed_rpm = (float)encoder->count / ENCODER_PPR * 60.0f / SAMPLING_TIME_S;
}

// 更新编码器数据 (20ms周期调用)
void encoder_update(void)
{
    encoder_update_single(&left_encoder);
    encoder_update_single(&right_encoder);
}

// 获取左轮速度 (cm/s)
float encoder_get_left_speed(void)
{
    return left_encoder.speed_cm_s;
}

// 获取右轮速度 (cm/s)
float encoder_get_right_speed(void)
{
    return right_encoder.speed_cm_s;
}

// 获取左轮转速 (RPM)
float encoder_get_left_rpm(void)
{
    return left_encoder.speed_rpm;
}

// 获取右轮转速 (RPM)
float encoder_get_right_rpm(void)
{
    return right_encoder.speed_rpm;
}
