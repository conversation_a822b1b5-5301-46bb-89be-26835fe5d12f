# 🚗 STM32F407智能循迹小车系统

## 📋 项目概述

本项目是一个基于STM32F407微控制器的智能循迹小车系统，采用先进的任务调度架构和模块化设计，具备完整的循迹功能、多种运行模式和丰富的调试工具。

### 🎯 核心特性
- **🎯 智能循迹**: 7路灰度传感器，精确线路跟踪
- **🔄 多种模式**: 停止/直线/循环/预留四种运行模式
- **⚡ 任务调度**: 基于时间片的多任务调度系统
- **📊 实时监控**: DMA串口通信，纸飞机调试工具
- **🏗️ 模块化设计**: 基于motor_driver架构的底层驱动
- **🔧 智能控制**: 编码器反馈，实时速度调节

### 🏗️ 系统架构
```
STM32F407循迹小车系统
├── 硬件层: STM32F407VET6 + MG513XP28双电机 + 7路传感器
├── 驱动层: 电机驱动(motor_driver架构) + 传感器驱动 + 通信驱动
├── 应用层: 任务调度器 + 运动控制 + 模式管理
└── 调试层: 串口监控 + 纸飞机工具 + LED指示
```

## 🚀 快速开始

### 1. 硬件配置
- **主控**: STM32F407VET6 (168MHz)
- **电机**: MG513XP28双电机 (28:1减速比)
- **传感器**: 7路灰度传感器阵列
- **通信**: UART1 DMA (115200波特率)

### 2. 软件环境
- **IDE**: Keil MDK-ARM v5.06+
- **编译器**: ARM Compiler v5.06
- **调试**: ST-Link SWD

### 3. 运行模式
- **模式0**: 停止模式
- **模式1**: 直线行驶至黑线停止
- **模式2**: 完整循迹任务 (A→B→C→D→A)
- **模式3**: 预留模式

### 4. 操作方法
1. 按键0/1预选模式
2. 按键2确认执行
3. 观察串口输出
4. 使用纸飞机工具查看实时数据

## 📁 项目结构

```
tracking_car/
├── Core/                      # STM32 HAL核心文件
├── APP/                       # 应用层代码
│   ├── car.c/h               # 电机驱动 (基于motor_driver架构)
│   ├── Gray.c/h              # 传感器驱动
│   ├── key.c/h               # 按键驱动
│   ├── my_uart.c/h           # 串口驱动
│   └── scheduler.c/h         # 任务调度
├── docs/                      # 完整项目文档
│   ├── README.md             # 文档总览
│   ├── final/                # 快速入门和系统文档
│   ├── features/             # 功能特性文档
│   ├── development/          # 开发技术文档
│   └── guides/               # 使用指南
└── motor_driver/             # 参考架构
```

## 📚 文档导航

### 🚀 快速入门
- [📖 快速入门指南](docs/final/Quick_Start_Guide.md) - 5分钟上手指南
- [✅ 系统验证清单](docs/final/System_Verification_Checklist.md) - 功能验证
- [📊 完整系统文档](docs/final/Complete_System_Documentation.md) - 系统总览

### 🔧 技术文档
- [🏗️ 技术架构总览](docs/development/Technical_Architecture_Overview.md) - 系统架构
- [🚗 电机驱动架构](docs/development/Car_Driver_Rewrite_Based_On_Motor_Driver.md) - motor_driver架构
- [📡 DMA串口通信](docs/features/UART_DMA_Guide.md) - 通信实现

### 📊 功能特性
- [🎮 模式系统总览](docs/features/Mode_System_Overview.md) - 运行模式
- [🛤️ 循迹实现](docs/features/Line_Following_Implementation.md) - 循迹算法
- [📈 纸飞机调试工具](docs/features/PaperPlane_Curve_Plotting_Guide.md) - 实时监控

## 🎯 技术亮点

### 先进架构设计
- **分层架构**: 硬件抽象层、驱动层、应用层、调试层
- **任务调度**: 基于时间片的多任务调度系统
- **模块化设计**: 基于motor_driver架构的电机驱动

### 智能控制算法
- **7传感器循迹**: 加权算法精确计算线位置
- **实时速度控制**: 编码器反馈闭环控制
- **多模式运行**: 灵活的运行模式切换

### 完整调试体系
- **串口监控**: 实时状态和数据显示
- **纸飞机工具**: 实时速度曲线绘制
- **LED指示**: 直观的状态指示

## 📞 技术支持

### 调试工具
- **串口监控**: 波特率115200，实时状态显示
- **纸飞机工具**: TEXT协议，实时曲线绘制
- **LED指示**: 系统状态和模式指示

### 常见问题
1. **循迹偏移**: 检查传感器校准和权重算法
2. **速度异常**: 验证编码器连接和PWM配置
3. **通信问题**: 确认UART配置和DMA设置

### 获取帮助
- 查看 [docs/](docs/) 目录下的完整文档
- 参考 [快速入门指南](docs/final/Quick_Start_Guide.md)
- 使用 [系统验证清单](docs/final/System_Verification_Checklist.md) 排查问题

---

**项目版本**: v2.0  
**最后更新**: 2025-07-07  
**维护团队**: STM32循迹小车开发团队
