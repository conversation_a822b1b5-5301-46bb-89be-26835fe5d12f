#include "Gray.h"
#include <math.h>

// 8路灰度传感器权重配置
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};

// 全局变量
uint8_t gray_digital_data = 0;      // 数字传感器数据
float g_line_position_error = 0.0f; // 循迹误差值

/**
 * @brief 灰度传感器初始化
 */
void Gray_Init(void)
{
    // GPIO已在CubeMX中配置，无需额外初始化
    g_line_position_error = 0.0f;
}

/**
 * @brief 读取灰度传感器数字数据
 * @param gray_buf 传感器数据缓冲区
 */
void Gray_get(uint8_t *gray_buf)
{
	// 7路灰度传感器读取 (忽略P_1，使用P_2到P_8)
	gray_buf[0] = 1;  // P_1忽略，设为1 (白线状态)
	gray_buf[1] = HAL_GPIO_ReadPin(P_2_GPIO_Port, P_2_Pin);   // 传感器2
	gray_buf[2] = HAL_GPIO_ReadPin(P_3_GPIO_Port, P_3_Pin);   // 传感器3
	gray_buf[3] = HAL_GPIO_ReadPin(P_4_GPIO_Port, P_4_Pin);   // 传感器4
	gray_buf[4] = HAL_GPIO_ReadPin(P_5_GPIO_Port, P_5_Pin);   // 传感器5
	gray_buf[5] = HAL_GPIO_ReadPin(P_6_GPIO_Port, P_6_Pin);   // 传感器6
	gray_buf[6] = HAL_GPIO_ReadPin(P_7_GPIO_Port, P_7_Pin);   // 传感器7
	gray_buf[7] = HAL_GPIO_ReadPin(P_8_GPIO_Port, P_8_Pin);   // 传感器8
}

/**
 * @brief 灰度传感器任务 - 基于2024_H_Car蓝本的巡线算法
 */
void Gray_Task(void)
{
    uint8_t gray_buf[8];
    Gray_get(gray_buf);

    // 将数组数据转换为数字位数据 (基于蓝本格式)
    gray_digital_data = 0;
    for(uint8_t i = 0; i < 8; i++) {
        if(gray_buf[i] == 0) {  // 0表示检测到黑线
            gray_digital_data |= (1 << i);
        }
    }

    // 基于2024_H_Car蓝本的巡线算法
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++) {
        if((gray_digital_data >> i) & 0x01) {
            weighted_sum += gray_weights[i];
            black_line_count++;
        }
    }

    // 计算位置误差 (基于蓝本算法)
    if(black_line_count > 0) {
        g_line_position_error = weighted_sum / (float)black_line_count;
    } else {
        // 没有检测到黑线，保持上次的误差值
        // g_line_position_error 保持不变
    }
}

/**
 * @brief 获取循迹位置误差
 * @return 位置误差值 (-4.0 到 +4.0)
 */
float Gray_Get_Position_Error(void)
{
    return g_line_position_error;
}

/**
 * @brief 获取检测到黑线的传感器数量
 * @return 黑线传感器数量
 */
uint8_t Gray_Get_Active_Sensor_Count(void)
{
    uint8_t count = 0;
    for(uint8_t i = 0; i < 8; i++) {
        if((gray_digital_data >> i) & 0x01) {
            count++;
        }
    }
    return count;
}

