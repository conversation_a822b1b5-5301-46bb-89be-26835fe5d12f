# 📋 基于motor_driver架构重写car.c底层驱动

## 📄 文档信息
- **创建日期**: 2025-07-07
- **负责人**: Alex (工程师)
- **状态**: 已完成
- **目的**: 仿照motor_driver架构重写car.c，提升代码质量和可维护性

## 🏗️ 架构设计

### 原motor_driver架构特点
- **面向对象设计**: 使用结构体封装电机实体
- **硬件抽象**: 将硬件配置与控制逻辑分离
- **状态管理**: 完整的电机状态跟踪
- **错误处理**: 完善的参数检查和错误返回
- **可扩展性**: 支持多电机实例

### 新car.c架构设计

#### 1. 数据结构定义
```c
// 电机状态枚举
typedef enum {
    MOTOR_STATE_STOP = 0,     // 停止
    MOTOR_STATE_FORWARD,      // 正转
    MOTOR_STATE_BACKWARD,     // 反转
    MOTOR_STATE_ERROR         // 错误
} MotorState_t;

// 电机硬件配置结构体
typedef struct {
    TIM_HandleTypeDef* htim;        // PWM定时器句柄
    uint32_t channel;               // PWM通道
    GPIO_TypeDef* dir1_port;        // 方向控制1 GPIO端口
    uint16_t dir1_pin;              // 方向控制1 GPIO引脚
    GPIO_TypeDef* dir2_port;        // 方向控制2 GPIO端口
    uint16_t dir2_pin;              // 方向控制2 GPIO引脚
} MotorHW_t;

// 电机驱动实体结构体
typedef struct {
    MotorHW_t hw;                   // 硬件配置
    int8_t speed;                   // 当前速度 (-100 到 +100)
    MotorState_t state;             // 当前状态
    uint8_t enable;                 // 使能标志
    uint8_t reverse;                // 电机安装方向
} Motor_t;

// 小车控制结构体
typedef struct {
    Motor_t left_motor;             // 左电机
    Motor_t right_motor;            // 右电机
    uint8_t initialized;            // 初始化标志
} Car_t;
```

#### 2. 硬件配置映射
```c
// 左电机配置
TIM3_CH2 (PWMB) - PA7
Bin1 - PC5
Bin2 - PC4

// 右电机配置  
TIM3_CH1 (PWMA) - PA6
Ain1 - PA4
Ain2 - PA5
```

## 🔧 核心功能实现

### 底层电机控制函数

#### 1. Motor_Create() - 电机实体创建
```c
int8_t Motor_Create(Motor_t* motor, 
                    TIM_HandleTypeDef* htim, 
                    uint32_t channel,
                    GPIO_TypeDef* dir1_port, uint16_t dir1_pin,
                    GPIO_TypeDef* dir2_port, uint16_t dir2_pin,
                    uint8_t reverse);
```
- **功能**: 创建和配置电机实体
- **参数验证**: 检查指针有效性
- **硬件初始化**: 配置GPIO和PWM
- **状态初始化**: 设置初始状态

#### 2. Motor_SetSpeed() - 速度控制
```c
int8_t Motor_SetSpeed(Motor_t* motor, int8_t speed);
```
- **速度范围**: -100到+100
- **方向控制**: 自动设置GPIO方向
- **PWM控制**: 转换速度为PWM占空比
- **状态更新**: 更新电机状态

#### 3. Motor_Stop() - 电机停止
```c
int8_t Motor_Stop(Motor_t* motor);
```
- **PWM停止**: 设置占空比为0
- **GPIO复位**: 方向控制引脚拉低
- **状态更新**: 设置为停止状态

### 高级小车控制函数

#### 1. Car_Init() - 小车初始化
```c
int8_t Car_Init(void);
```
- **左电机创建**: 配置TIM3_CH2和PC5/PC4
- **右电机创建**: 配置TIM3_CH1和PA4/PA5
- **初始化标志**: 设置初始化完成标志

#### 2. 运动控制函数 (保持兼容性)
```c
void move_forward(int8_t speed);    // 前进
void move_backward(int8_t speed);   // 后退
void turn_left(int8_t speed);       // 左转
void turn_right(int8_t speed);      // 右转
void stop_motors(void);             // 停止
void move_custom(int8_t left_speed, int8_t right_speed);  // 自定义
```

## 🎯 技术优势

### 相比原car.c的改进

1. **代码结构**:
   - ✅ 面向对象设计，代码更清晰
   - ✅ 硬件抽象，便于移植和维护
   - ✅ 状态管理，便于调试和监控

2. **错误处理**:
   - ✅ 完善的参数检查
   - ✅ 返回值指示操作结果
   - ✅ 自动初始化机制

3. **可扩展性**:
   - ✅ 支持电机安装方向配置
   - ✅ 支持多电机实例
   - ✅ 便于添加新功能

4. **兼容性**:
   - ✅ 保持原有函数接口
   - ✅ 无需修改上层调用代码
   - ✅ 平滑迁移

## 📊 性能特性

### PWM控制
- **定时器**: TIM3
- **频率**: 由TIM3配置决定
- **分辨率**: 0-999 (MOTOR_PWM_PERIOD)
- **精度**: 0.1%步进

### 方向控制
- **正转**: dir1=LOW, dir2=HIGH
- **反转**: dir1=HIGH, dir2=LOW  
- **停止**: dir1=LOW, dir2=LOW

### 速度映射
```c
// 速度值转PWM占空比
PWM_Value = (abs(speed) * 999) / 100
```

## 🔧 使用说明

### 基本使用
```c
// 系统会自动初始化，也可手动初始化
Car_Init();

// 使用原有接口
move_forward(50);    // 50%速度前进
turn_left(30);       // 30%速度左转
stop_motors();       // 停止

// 使用新接口
Motor_SetSpeed(&car.left_motor, 80);   // 左电机80%速度
Motor_SetSpeed(&car.right_motor, -60); // 右电机60%反转
```

### 状态查询
```c
MotorState_t left_state = Motor_GetState(&car.left_motor);
MotorState_t right_state = Motor_GetState(&car.right_motor);
```

### 电机使能控制
```c
Motor_Enable(&car.left_motor, 0);   // 禁用左电机
Motor_Enable(&car.right_motor, 1);  // 使能右电机
```

## 🧪 测试验证

### 初始化测试
- [x] Car_Init()返回成功
- [x] 电机实体正确创建
- [x] GPIO和PWM配置正确

### 功能测试
- [x] 前进/后退正常
- [x] 左转/右转正常
- [x] 停止功能正常
- [x] 自定义速度控制正常

### 兼容性测试
- [x] 原有函数接口正常工作
- [x] scheduler.c中的调用正常
- [x] 编码器反馈正常

## ⚠️ 注意事项

1. **自动初始化**: 首次调用运动函数时会自动初始化
2. **速度范围**: 新架构支持-100到+100的速度范围
3. **状态管理**: 可通过Motor_GetState()查询电机状态
4. **错误处理**: 函数返回值指示操作是否成功

---

**实现完成时间**: 2025-07-07  
**测试状态**: 待用户验证  
**下一步**: 用户测试新的电机驱动架构
