# 🔌 OLED I2C引脚配置文档

## 📋 文档信息
- **创建日期**: 2025-07-07
- **负责人**: <PERSON> (工程师)
- **状态**: OLED I2C引脚配置完成
- **目的**: 配置OLED显示模块的I2C通信引脚

## 🎯 I2C引脚配置

### 当前配置
- **SCL引脚**: PE14 (GPIOE, GPIO_PIN_14)
- **SDA引脚**: PE15 (GPIOE, GPIO_PIN_15)
- **通信方式**: 软件I2C (GPIO模拟)
- **OLED地址**: 0x78

### 硬件连接
```
OLED模块    STM32F407
VCC    →    3.3V
GND    →    GND
SCL    →    PE14
SDA    →    PE15
```

## 🔧 软件I2C实现

### 引脚定义
```c
// OLED软件I2C引脚配置
#define OLED_SCL_PIN    GPIO_PIN_14  // PE14 - SCL
#define OLED_SDA_PIN    GPIO_PIN_15  // PE15 - SDA
#define OLED_SCL_PORT   GPIOE
#define OLED_SDA_PORT   GPIOE
```

### I2C操作宏
```c
#define OLED_SCL_HIGH() HAL_GPIO_WritePin(OLED_SCL_PORT, OLED_SCL_PIN, GPIO_PIN_SET)
#define OLED_SCL_LOW()  HAL_GPIO_WritePin(OLED_SCL_PORT, OLED_SCL_PIN, GPIO_PIN_RESET)
#define OLED_SDA_HIGH() HAL_GPIO_WritePin(OLED_SDA_PORT, OLED_SDA_PIN, GPIO_PIN_SET)
#define OLED_SDA_LOW()  HAL_GPIO_WritePin(OLED_SDA_PORT, OLED_SDA_PIN, GPIO_PIN_RESET)
```

### GPIO初始化
```c
void OLED_Init(void)
{
    // 配置GPIO
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIOE时钟
    __HAL_RCC_GPIOE_CLK_ENABLE();
    
    // 配置SCL和SDA引脚
    GPIO_InitStruct.Pin = OLED_SCL_PIN | OLED_SDA_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;  // 开漏输出
    GPIO_InitStruct.Pull = GPIO_PULLUP;          // 上拉
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
    
    // 初始化引脚状态
    OLED_SCL_HIGH();
    OLED_SDA_HIGH();
}
```

## 📊 I2C时序实现

### 起始信号
```c
static void OLED_I2C_Start(void)
{
    OLED_SDA_HIGH();
    OLED_SCL_HIGH();
    OLED_Delay_us(5);
    OLED_SDA_LOW();
    OLED_Delay_us(5);
    OLED_SCL_LOW();
}
```

### 停止信号
```c
static void OLED_I2C_Stop(void)
{
    OLED_SDA_LOW();
    OLED_SCL_HIGH();
    OLED_Delay_us(5);
    OLED_SDA_HIGH();
    OLED_Delay_us(5);
}
```

### 发送字节
```c
static void OLED_I2C_SendByte(uint8_t byte)
{
    for(int i = 7; i >= 0; i--)
    {
        if(byte & (1 << i))
            OLED_SDA_HIGH();
        else
            OLED_SDA_LOW();
        
        OLED_Delay_us(2);
        OLED_SCL_HIGH();
        OLED_Delay_us(5);
        OLED_SCL_LOW();
        OLED_Delay_us(2);
    }
    
    // 等待ACK
    OLED_SDA_HIGH();
    OLED_Delay_us(2);
    OLED_SCL_HIGH();
    OLED_Delay_us(5);
    OLED_SCL_LOW();
    OLED_Delay_us(2);
}
```

## 🎮 OLED通信协议

### 写命令
```c
void OLED_Write_cmd(uint8_t cmd)
{
    OLED_I2C_Start();
    OLED_I2C_SendByte(OLED_ADDR);  // 发送设备地址 0x78
    OLED_I2C_SendByte(0x00);       // 命令模式
    OLED_I2C_SendByte(cmd);        // 发送命令
    OLED_I2C_Stop();
}
```

### 写数据
```c
void OLED_Write_data(uint8_t data)
{
    OLED_I2C_Start();
    OLED_I2C_SendByte(OLED_ADDR);  // 发送设备地址 0x78
    OLED_I2C_SendByte(0x40);       // 数据模式
    OLED_I2C_SendByte(data);       // 发送数据
    OLED_I2C_Stop();
}
```

## ⚡ 性能参数

### 时序参数
- **I2C速度**: 约100-400kHz
- **延时精度**: 微秒级 (基于168MHz系统时钟)
- **通信可靠性**: 高 (软件控制，时序精确)

### 延时函数
```c
static void OLED_Delay_us(uint32_t us)
{
    uint32_t delay = us * 168 / 4;  // 168MHz系统时钟
    while(delay--);
}
```

## 🔧 引脚修改指南

### 如需修改引脚
1. **修改引脚定义**:
   ```c
   #define OLED_SCL_PIN    GPIO_PIN_XX  // 修改为实际SCL引脚
   #define OLED_SDA_PIN    GPIO_PIN_XX  // 修改为实际SDA引脚
   #define OLED_SCL_PORT   GPIOX        // 修改为实际GPIO端口
   #define OLED_SDA_PORT   GPIOX        // 修改为实际GPIO端口
   ```

2. **修改时钟使能**:
   ```c
   __HAL_RCC_GPIOX_CLK_ENABLE();  // 修改为对应的GPIO时钟
   ```

3. **重新编译**: 修改后重新编译工程

### 推荐引脚
- **PE14/PE15**: 当前配置，无冲突
- **PB8/PB9**: I2C1硬件引脚，也可用作软件I2C
- **PB10/PB11**: I2C2硬件引脚，也可用作软件I2C

## 🔌 硬件注意事项

### 电源要求
- **OLED模块**: 3.3V供电
- **逻辑电平**: 3.3V TTL
- **功耗**: 约20-50mA

### 连接要求
- **上拉电阻**: I2C线路需要上拉电阻(通常OLED模块自带)
- **线长**: 建议I2C线长不超过30cm
- **干扰**: 避免与高频信号线并行走线

### 调试建议
1. **示波器检查**: 检查SCL和SDA信号波形
2. **地址确认**: 确认OLED模块I2C地址为0x78
3. **供电检查**: 确认OLED模块3.3V供电正常
4. **连线检查**: 确认SCL/SDA连线正确

## 📱 集成状态

### 任务调度
```c
{oled_task,200,0},    // OLED任务，200ms周期
```

### 初始化流程
```c
void scheduler_init(void)
{
    Motor_App_Init();     // 初始化电机系统
    Mpu6050_Init();       // 初始化MPU6050系统
    OLED_App_Init();      // 初始化OLED显示 ✅
}
```

### 显示功能
- ✅ **4个页面**: 系统状态、传感器、电机、MPU6050
- ✅ **实时更新**: 200ms周期自动刷新
- ✅ **字体支持**: 6x8和8x16两种字体
- ✅ **完整API**: 字符、字符串、数字、浮点数显示

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**配置状态**: ✅ OLED I2C引脚配置完成  
**硬件要求**: 🔌 SSD1306 OLED模块连接到PE14(SCL)/PE15(SDA)
