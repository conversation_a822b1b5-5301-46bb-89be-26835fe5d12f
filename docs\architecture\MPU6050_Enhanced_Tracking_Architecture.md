# 🏗️ MPU6050增强循迹系统架构设计

## 📋 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-07
- **负责人**: Bob (架构师)
- **状态**: 架构设计完成
- **目的**: 设计MPU6050与循迹算法融合的技术架构

## 🎯 架构概述

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    循迹控制系统                              │
├─────────────────────────────────────────────────────────────┤
│  传感器层    │  数据融合层   │  控制算法层   │  执行层      │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 灰度传感器   │             │             │             │
│ (Gray)      │   传感器    │   增强循迹   │   电机控制   │
│             │   数据融合   │   算法      │   (Motor)   │
│ MPU6050     │   (Fusion)  │  (Enhanced  │             │
│ (姿态)      │             │  Tracking)  │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### 核心组件
1. **传感器数据采集**: 灰度传感器 + MPU6050
2. **数据融合引擎**: 多传感器数据融合
3. **增强循迹算法**: 姿态辅助的智能循迹
4. **电机控制执行**: 精确的电机控制输出

## 📊 数据流设计

### 数据流图
```
灰度传感器 ──┐
            ├──→ 数据融合 ──→ 增强循迹算法 ──→ 电机控制
MPU6050 ────┘      ↓              ↓
                状态监控      参数调整
                   ↓              ↓
                安全检查      速度控制
```

### 数据结构定义
```c
// 传感器数据结构
typedef struct {
    // 灰度传感器数据
    uint8_t gray_sensors[7];
    float line_position;
    uint8_t line_detected;
    
    // MPU6050姿态数据
    float pitch;    // 俯仰角
    float roll;     // 横滚角
    float yaw;      // 偏航角
    
    // 时间戳
    unsigned long timestamp;
} sensor_data_t;

// 融合控制数据
typedef struct {
    // 基础循迹控制
    int8_t line_correction;
    float base_speed;
    
    // 姿态增强控制
    int8_t yaw_correction;
    float attitude_speed_factor;
    int8_t stability_correction;
    
    // 最终输出
    float left_speed;
    float right_speed;
    
    // 状态标志
    uint8_t slope_mode;
    uint8_t stability_warning;
    uint8_t emergency_stop;
} control_data_t;
```

## 🔧 核心算法设计

### 1. 数据融合算法

#### 传感器数据融合
```c
void sensor_data_fusion(sensor_data_t* data) {
    // 读取灰度传感器
    read_gray_sensors(data->gray_sensors);
    data->line_position = calculate_line_position(data->gray_sensors);
    data->line_detected = detect_line(data->gray_sensors);
    
    // 读取MPU6050数据
    data->pitch = Pitch;
    data->roll = Roll;
    data->yaw = Yaw;
    
    // 数据有效性检查
    validate_sensor_data(data);
    
    data->timestamp = HAL_GetTick();
}
```

#### 数据有效性验证
```c
uint8_t validate_sensor_data(sensor_data_t* data) {
    // 检查姿态数据合理性
    if(fabs(data->pitch) > 45.0f || fabs(data->roll) > 45.0f) {
        return 0; // 数据异常
    }
    
    // 检查灰度传感器数据
    if(!data->line_detected && previous_line_detected) {
        // 线路丢失处理
        handle_line_lost();
    }
    
    return 1; // 数据有效
}
```

### 2. 增强循迹算法

#### 主控制算法
```c
void enhanced_tracking_control(sensor_data_t* sensor, control_data_t* control) {
    // 1. 基础循迹控制
    control->line_correction = calculate_line_correction(sensor->line_position);
    control->base_speed = BASE_TRACKING_SPEED;
    
    // 2. 姿态辅助转向
    control->yaw_correction = calculate_yaw_correction(sensor->yaw);
    
    // 3. 坡道自适应控制
    control->attitude_speed_factor = calculate_slope_factor(sensor->pitch);
    
    // 4. 稳定性监控
    control->stability_correction = calculate_stability_correction(sensor->roll);
    
    // 5. 安全检查
    control->emergency_stop = check_emergency_conditions(sensor);
    
    // 6. 最终控制输出
    calculate_final_motor_speeds(control);
}
```

#### 姿态辅助转向算法
```c
int8_t calculate_yaw_correction(float current_yaw) {
    static float target_yaw = 0.0f;
    static uint8_t yaw_initialized = 0;
    
    // 初始化目标偏航角
    if(!yaw_initialized) {
        target_yaw = current_yaw;
        yaw_initialized = 1;
        return 0;
    }
    
    // 计算偏航角误差
    float yaw_error = target_yaw - current_yaw;
    
    // 处理±180°跳变
    if(yaw_error > 180.0f) yaw_error -= 360.0f;
    if(yaw_error < -180.0f) yaw_error += 360.0f;
    
    // PID控制
    int8_t correction = (int8_t)(yaw_error * YAW_KP);
    
    // 限制输出范围
    if(correction > MAX_YAW_CORRECTION) correction = MAX_YAW_CORRECTION;
    if(correction < -MAX_YAW_CORRECTION) correction = -MAX_YAW_CORRECTION;
    
    return correction;
}
```

#### 坡道自适应算法
```c
float calculate_slope_factor(float pitch) {
    float slope_factor = 1.0f;
    
    if(fabs(pitch) > SLOPE_THRESHOLD) {
        if(pitch > 0) {
            // 上坡：降低速度，增加功率
            slope_factor = UPHILL_SPEED_FACTOR;
        } else {
            // 下坡：控制速度，增强制动
            slope_factor = DOWNHILL_SPEED_FACTOR;
        }
    }
    
    return slope_factor;
}
```

#### 稳定性监控算法
```c
int8_t calculate_stability_correction(float roll) {
    int8_t correction = 0;
    
    if(fabs(roll) > STABILITY_THRESHOLD) {
        // 稳定性修正：向倾斜反方向调整
        correction = (int8_t)(-roll * STABILITY_KP);
        
        // 限制修正幅度
        if(correction > MAX_STABILITY_CORRECTION) 
            correction = MAX_STABILITY_CORRECTION;
        if(correction < -MAX_STABILITY_CORRECTION) 
            correction = -MAX_STABILITY_CORRECTION;
    }
    
    return correction;
}
```

### 3. 最终控制输出

#### 电机速度计算
```c
void calculate_final_motor_speeds(control_data_t* control) {
    // 应用坡道因子
    float adjusted_speed = control->base_speed * control->attitude_speed_factor;
    
    // 组合所有修正量
    int8_t total_correction = control->line_correction + 
                             control->yaw_correction + 
                             control->stability_correction;
    
    // 计算左右轮速度
    control->left_speed = adjusted_speed + total_correction;
    control->right_speed = adjusted_speed - total_correction;
    
    // 速度限制
    limit_motor_speeds(control);
    
    // 紧急停车检查
    if(control->emergency_stop) {
        control->left_speed = 0;
        control->right_speed = 0;
    }
}
```

## ⚙️ 参数配置

### 控制参数
```c
// 姿态控制参数
#define YAW_KP                  0.5f    // 偏航角比例系数
#define MAX_YAW_CORRECTION      20      // 最大偏航修正
#define STABILITY_KP            1.0f    // 稳定性比例系数
#define MAX_STABILITY_CORRECTION 15     // 最大稳定性修正

// 坡道控制参数
#define SLOPE_THRESHOLD         5.0f    // 坡道检测阈值(度)
#define UPHILL_SPEED_FACTOR     0.7f    // 上坡速度因子
#define DOWNHILL_SPEED_FACTOR   0.8f    // 下坡速度因子

// 安全阈值
#define STABILITY_THRESHOLD     10.0f   // 稳定性警告阈值(度)
#define EMERGENCY_ROLL_LIMIT    25.0f   // 紧急停车横滚角限制
#define EMERGENCY_PITCH_LIMIT   30.0f   // 紧急停车俯仰角限制
```

### 速度参数
```c
// 基础速度参数
#define BASE_TRACKING_SPEED     25.0f   // 基础循迹速度
#define MAX_MOTOR_SPEED         50.0f   // 最大电机速度
#define MIN_MOTOR_SPEED         5.0f    // 最小电机速度
```

## 🔄 任务调度集成

### 任务优先级
```c
// 更新任务调度表
static task_t scheduler_task[] =
{
    {key_task,100,0},           // 按键任务，100ms周期
    {uart_task,50,0},           // 串口任务，50ms周期
    {sensor_fusion_task,20,0},  // 传感器融合，20ms周期 (新增)
    {enhanced_tracking_task,20,0}, // 增强循迹，20ms周期 (新增)
    {motor_control_task,20,0},  // 电机控制，20ms周期
    {gray_task,30,0},           // 灰度传感器，30ms周期
    {mpu_task,20,0},            // MPU6050任务，20ms周期
};
```

### 数据同步机制
```c
// 全局数据结构
extern sensor_data_t g_sensor_data;
extern control_data_t g_control_data;

// 数据同步标志
volatile uint8_t sensor_data_ready = 0;
volatile uint8_t control_data_ready = 0;
```

## 📊 性能优化

### 计算优化
- **浮点运算优化**: 减少不必要的浮点计算
- **查表法**: 三角函数使用查表法
- **定点运算**: 部分计算使用定点数

### 内存优化
- **静态分配**: 避免动态内存分配
- **数据复用**: 复用临时变量
- **结构体对齐**: 优化数据结构对齐

### 实时性保证
- **任务优先级**: 关键任务高优先级
- **中断处理**: 最小化中断处理时间
- **看门狗**: 系统稳定性监控

## 🔒 安全设计

### 故障处理
```c
// 传感器故障处理
void handle_sensor_failure(uint8_t sensor_type) {
    switch(sensor_type) {
        case MPU6050_FAILURE:
            // 降级到纯灰度循迹
            disable_attitude_control();
            break;
        case GRAY_SENSOR_FAILURE:
            // 使用姿态保持直线
            enable_attitude_only_mode();
            break;
    }
}
```

### 紧急停车机制
```c
uint8_t check_emergency_conditions(sensor_data_t* data) {
    // 检查危险倾斜
    if(fabs(data->roll) > EMERGENCY_ROLL_LIMIT ||
       fabs(data->pitch) > EMERGENCY_PITCH_LIMIT) {
        return 1; // 紧急停车
    }
    
    // 检查传感器异常
    if(!validate_sensor_data(data)) {
        return 1; // 紧急停车
    }
    
    return 0; // 正常运行
}
```

---

**文档版本**: v1.0  
**最后更新**: 2025-07-07  
**架构状态**: ✅ 设计完成  
**下一步**: 代码实现
