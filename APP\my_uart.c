#include "my_uart.h"

uint32_t uart_rx_ticks = 0;
uint16_t uart_rx_index = 0;
uint8_t uart_rx_buffer[128] = {0};

// DMA发送缓冲区和状态
static uint8_t uart_tx_buffer[512];
static volatile uint8_t uart_tx_busy = 0;

int my_printf(UART_HandleTypeDef *huart,const char *format,...)
{
	char buffer[512];
	va_list arg;
	int len;

	va_start(arg,format);
	len = vsnprintf(buffer,sizeof(buffer),format,arg);
	va_end(arg);

	// 等待上次DMA传输完成
	while(uart_tx_busy);

	// 复制数据到DMA缓冲区
	memcpy(uart_tx_buffer, buffer, len);

	// 设置忙标志
	uart_tx_busy = 1;

	// 启动DMA传输
	if(HAL_UART_Transmit_DMA(huart, uart_tx_buffer, (uint16_t)len) != HAL_OK)
	{
		// DMA传输失败，回退到阻塞传输
		uart_tx_busy = 0;
		HAL_UART_Transmit(huart,(uint8_t *)buffer,(uint16_t)len,0xFF);
	}

	return len;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if(huart->Instance == USART1)
	{
		uart_rx_ticks = uwTick;

		uart_rx_index++;

		HAL_UART_Receive_IT(&huart1,&uart_rx_buffer[uart_rx_index],1);
	}
}

// DMA发送完成回调函数
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
	if(huart->Instance == USART1)
	{
		// 清除忙标志，允许下次DMA传输
		uart_tx_busy = 0;
	}
}

// DMA发送错误回调函数
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
	if(huart->Instance == USART1)
	{
		// 发生错误，清除忙标志
		uart_tx_busy = 0;
	}
}

// 非阻塞版本的printf (如果DMA忙则丢弃数据)
int my_printf_nb(UART_HandleTypeDef *huart,const char *format,...)
{
	// 如果DMA忙，直接返回
	if(uart_tx_busy) return 0;

	char buffer[512];
	va_list arg;
	int len;

	va_start(arg,format);
	len = vsnprintf(buffer,sizeof(buffer),format,arg);
	va_end(arg);

	// 复制数据到DMA缓冲区
	memcpy(uart_tx_buffer, buffer, len);

	// 设置忙标志
	uart_tx_busy = 1;

	// 启动DMA传输
	if(HAL_UART_Transmit_DMA(huart, uart_tx_buffer, (uint16_t)len) != HAL_OK)
	{
		// DMA传输失败
		uart_tx_busy = 0;
		return 0;
	}

	return len;
}

// 检查DMA是否忙碌
uint8_t uart_dma_is_busy(void)
{
	return uart_tx_busy;
}

// 等待DMA传输完成
void uart_dma_wait_complete(void)
{
	while(uart_tx_busy);
}
