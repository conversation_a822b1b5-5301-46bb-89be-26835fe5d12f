#ifndef MY_UART_H
#define MY_UART_H

#include "mydefine.h"

// 函数声明
int my_printf(UART_HandleTypeDef *huart,const char *format,...);      // 阻塞版本DMA printf
int my_printf_nb(UART_HandleTypeDef *huart,const char *format,...);   // 非阻塞版本DMA printf
uint8_t uart_dma_is_busy(void);                                       // 检查DMA状态
void uart_dma_wait_complete(void);                                    // 等待DMA完成

// 回调函数声明
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart);
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart);

#endif
