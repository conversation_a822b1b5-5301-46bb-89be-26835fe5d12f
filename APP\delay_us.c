#include "delay_us.h"

// DWT寄存器定义
#define DWT_CTRL    (*(volatile uint32_t*)0xE0001000)
#define DWT_CYCCNT  (*(volatile uint32_t*)0xE0001004)
#define DEM_CR      (*(volatile uint32_t*)0xE000EDFC)
#define DEM_CR_TRCENA (1 << 24)

// 系统时钟频率 (STM32F407通常为168MHz)
static uint32_t cpu_freq_mhz = 168;
static uint32_t timer_start = 0;

/**
 * @brief 初始化微秒延时功能
 */
void delay_us_init(void)
{
    // 获取系统时钟频率
    cpu_freq_mhz = HAL_RCC_GetHCLKFreq() / 1000000;
    
    // 使能DWT
    DEM_CR |= DEM_CR_TRCENA;
    
    // 使能CYCCNT
    DWT_CTRL |= 1;
    
    // 清零计数器
    DWT_CYCCNT = 0;
}

/**
 * @brief 微秒级延时
 * @param us 延时时间(微秒)
 */
void delay_us(uint32_t us)
{
    uint32_t start_tick = DWT_CYCCNT;
    uint32_t delay_ticks = us * cpu_freq_mhz;
    
    while((DWT_CYCCNT - start_tick) < delay_ticks);
}

/**
 * @brief 纳秒级延时 (最小精度约6ns@168MHz)
 * @param ns 延时时间(纳秒)
 */
void delay_ns(uint32_t ns)
{
    uint32_t start_tick = DWT_CYCCNT;
    uint32_t delay_ticks = (ns * cpu_freq_mhz) / 1000;
    
    // 最小延时为1个时钟周期
    if(delay_ticks == 0) delay_ticks = 1;
    
    while((DWT_CYCCNT - start_tick) < delay_ticks);
}

/**
 * @brief 获取当前微秒时间戳
 * @return 微秒时间戳
 */
uint32_t get_us_tick(void)
{
    return DWT_CYCCNT / cpu_freq_mhz;
}

/**
 * @brief 开始高精度计时
 */
void start_timer_us(void)
{
    timer_start = DWT_CYCCNT;
}

/**
 * @brief 获取计时时间
 * @return 从start_timer_us()开始的微秒数
 */
uint32_t get_timer_us(void)
{
    return (DWT_CYCCNT - timer_start) / cpu_freq_mhz;
}
